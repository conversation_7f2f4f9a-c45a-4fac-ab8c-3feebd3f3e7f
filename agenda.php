<?php

require_once __DIR__ . '/config/database.php';

// Crear conexión a la base de datos usando la configuración centralizada
$database = new Database();
$pdo = $database->getConnection();

?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Calendario de Agendamiento Médico</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8fafc;
            color: #334155;
            line-height: 1.6;
            overflow-x: hidden;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            color: white;
        }
        
        .header h1 {
            font-size: 28px;
            font-weight: 700;
        }
        
        .header-actions {
            display: flex;
            gap: 15px;
        }
        
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .btn-primary {
            background-color: #0ea5e9;
            color: white;
        }
        
        .btn-primary:hover {
            background-color: #0284c7;
            transform: translateY(-2px);
        }
        
        .btn-secondary {
            background-color: rgba(255, 255, 255, 0.2);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }
        
        .btn-secondary:hover {
            background-color: rgba(255, 255, 255, 0.3);
        }
        
        .user-info {
            display: flex;
            align-items: center;
            gap: 12px;
        }
        
        .user-avatar {
            width: 45px;
            height: 45px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 18px;
            border: 2px solid rgba(255, 255, 255, 0.3);
        }
        
        .calendar-navigation {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 25px;
            padding: 20px;
            background-color: #fff;
            border-radius: 12px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
        }
        
        .nav-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            padding: 12px 16px;
            cursor: pointer;
            font-size: 18px;
            color: white;
            transition: all 0.3s ease;
            width: 50px;
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .nav-button:hover {
            transform: scale(1.1);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        
        .current-month {
            font-size: 24px;
            font-weight: 700;
            color: #1e293b;
            text-align: center;
        }
        
        .view-controls {
            display: flex;
            gap: 10px;
        }
        
        .view-btn {
            padding: 8px 16px;
            border: 2px solid #e2e8f0;
            background: white;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 500;
        }
        
        .view-btn.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-color: transparent;
        }
        
        .main-content {
            display: grid;
            grid-template-columns: 1fr 300px;
            gap: 25px;
        }
        
        .calendar-container {
            background-color: #fff;
            border-radius: 15px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
            overflow: hidden;
        }
        
        .calendar-header {
            display: grid;
            grid-template-columns: repeat(5, 1fr);
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            border-bottom: 2px solid #cbd5e1;
        }
        
        .day-header {
            padding: 18px 10px;
            text-align: center;
            font-weight: 700;
            color: #475569;
            font-size: 15px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .calendar-body {
            display: grid;
            grid-template-columns: repeat(5, 1fr);
            min-height: 700px;
        }
        
        .day-column {
            border: 2px solid #e2e8f0;
            position: relative;
            transition: all 0.3s ease;
            margin: 2px;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }
        
        .day-column:hover {
            background-color: #fafbfc;
            border-color: #cbd5e1;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }
        
        .day-date {
            padding: 15px 10px;
            text-align: center;
            font-weight: 700;
            border-bottom: 2px solid #f1f5f9;
            background: linear-gradient(135deg, #e0f2fe 0%, #f0f9ff 100%);
            color: #1e293b;
            font-size: 14px;
            position: relative;
            border-radius: 8px 8px 0 0;
        }
        
        .day-date.today {
            background: linear-gradient(135deg, #0ea5e9 0%, #0284c7 100%);
            color: white;
        }
        
        .day-date.other-month {
            color: #94a3b8;
            background-color: #f8fafc;
        }
        
        .appointments-container {
            padding: 12px;
            min-height: 200px;
        }
        
        .appointment {
            background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
            border-left: 5px solid #0ea5e9;
            border-radius: 8px;
            padding: 12px;
            margin-bottom: 10px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .appointment::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #0ea5e9, #06b6d4);
        }
        
        .appointment:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(14, 165, 233, 0.2);
        }
        
        .appointment-time {
            font-weight: 700;
            color: #0284c7;
            font-size: 12px;
            margin-bottom: 3px;
            line-height: 1.3;
        }
        
        .appointment-patient {
            font-size: 11px;
            color: #64748b;
            margin-bottom: 6px;
            line-height: 1.3;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        
        .appointment-professional {
            color: #64748b;
            font-size: 12px;
        }
        
        .appointment-actions {
            position: absolute;
            top: 8px;
            right: 8px;
            display: flex;
            gap: 5px;
            opacity: 0;
            transition: opacity 0.3s ease;
        }
        
        .appointment:hover .appointment-actions {
            opacity: 1;
        }
        
        .appointment-action {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            border: none;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            transition: all 0.3s ease;
        }
        
        .appointment-action.edit {
            background-color: #fbbf24;
            color: white;
        }
        
        .appointment-action.delete {
            background-color: #ef4444;
            color: white;
        }
        
        .add-appointment-btn {
            width: 100%;
            padding: 15px;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            border: 2px dashed #cbd5e1;
            border-radius: 8px;
            color: #64748b;
            cursor: pointer;
            font-size: 14px;
            text-align: center;
            transition: all 0.3s ease;
            font-weight: 600;
            margin-top: 10px;
        }
        
        .add-appointment-btn:hover {
            background: linear-gradient(135deg, #e2e8f0 0%, #cbd5e1 100%);
            color: #475569;
            transform: translateY(-1px);
        }
        
        .sidebar {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }
        
        .sidebar-section {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
            margin-bottom: 20px;
            min-height: auto;
            height: auto;
            flex: none;
        }
        
        .sidebar-title {
            font-size: 18px;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .professional-list {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }
        
        .professional-item {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }
        
        .professional-item:hover {
            background-color: #f8fafc;
            border-color: #e2e8f0;
        }
        
        .professional-item.active {
            background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
            border-color: #0ea5e9;
        }
        
        .professional-avatar {
            width: 45px;
            height: 45px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            color: white;
            font-size: 16px;
        }
        
        .professional-info {
            flex: 1;
        }
        
        .professional-name {
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 2px;
        }
        
        .professional-specialty {
            font-size: 12px;
            color: #64748b;
        }
        
        .professional-status {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            background-color: #10b981;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
        }
        
        .stat-card {
            background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
            padding: 20px;
            border-radius: 12px;
            text-align: center;
            border: 1px solid #e2e8f0;
            transition: all 0.3s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }
        
        .stat-number {
            font-size: 28px;
            font-weight: 700;
            color: #0ea5e9;
            margin-bottom: 8px;
        }
        
        .stat-label {
            font-size: 12px;
            color: #64748b;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        /* Professional KPI Styles */
        .professional-kpis-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 8px;
            max-height: 400px;
            overflow-y: auto;
        }

        .professional-kpi-card {
            background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
            padding: 10px;
            border-radius: 8px;
            border: 1px solid #e2e8f0;
            transition: all 0.3s ease;
        }

        .professional-kpi-card:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
        }

        .professional-name-kpi {
            font-size: 12px;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 6px;
            text-align: center;
        }

        .kpi-metrics {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 4px;
            margin-bottom: 6px;
        }

        .kpi-metric {
            text-align: center;
            padding: 4px;
            background: #f8fafc;
            border-radius: 4px;
        }

        .kpi-metric-value {
            font-size: 14px;
            font-weight: 700;
            color: #0ea5e9;
            margin-bottom: 1px;
        }

        .kpi-metric-label {
            font-size: 8px;
            color: #64748b;
            font-weight: 600;
            text-transform: uppercase;
        }

        .kpi-percentages {
            display: flex;
            justify-content: space-between;
            margin-top: 6px;
            padding-top: 6px;
            border-top: 1px solid #e2e8f0;
        }

        .kpi-percentage {
            text-align: center;
            flex: 1;
        }

        .kpi-percentage-value {
            font-size: 11px;
            font-weight: 700;
            color: #059669;
        }

        .kpi-percentage-label {
            font-size: 8px;
            color: #64748b;
            font-weight: 600;
        }

        /* Box Availability Styles */
        .box-availability-item {
            background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
            padding: 12px;
            border-radius: 8px;
            border: 1px solid #e2e8f0;
            margin-bottom: 8px;
            transition: all 0.3s ease;
        }

        .box-availability-item:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
        }

        .box-name-availability {
            font-size: 14px;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 8px;
            text-align: center;
        }

        .box-shifts-availability {
            display: flex;
            justify-content: center;
            gap: 8px;
        }

        .shift-badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 10px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            transition: all 0.3s ease;
        }

        .shift-badge.available {
            background-color: #10b981;
            color: white;
            box-shadow: 0 2px 4px rgba(16, 185, 129, 0.3);
        }

        .shift-badge.occupied {
            background-color: #ef4444;
            color: white;
            box-shadow: 0 2px 4px rgba(239, 68, 68, 0.3);
        }

        .shift-badge.available:hover {
            background-color: #059669;
            transform: translateY(-1px);
        }

        .shift-badge.occupied:hover {
            background-color: #dc2626;
            transform: translateY(-1px);
        }

        .no-shifts-available {
            font-size: 10px;
            color: #9ca3af;
            font-style: italic;
            text-align: center;
            padding: 4px 8px;
            background-color: #f3f4f6;
            border-radius: 4px;
            border: 1px dashed #d1d5db;
        }

        /* Modal Styles */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 1000;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .modal-overlay.show {
            opacity: 1;
        }
        
        .modal {
            background: white;
            border-radius: 15px;
            width: 90%;
            max-width: 600px;
            max-height: 90vh;
            overflow-y: auto;
            transform: translateY(30px) scale(0.95);
            transition: all 0.3s ease;
            box-shadow: 0 20px 50px rgba(0, 0, 0, 0.2);
        }
        
        .modal-overlay.active .modal {
            transform: translateY(0) scale(1);
        }
        
        .modal-header {
            padding: 25px;
            border-bottom: 1px solid #e2e8f0;
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
            border-radius: 15px 15px 0 0;
        }
        
        .modal-title {
            font-size: 22px;
            font-weight: 700;
            color: #1e293b;
        }
        
        .modal-close {
            background: none;
            border: none;
            font-size: 28px;
            color: #64748b;
            cursor: pointer;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }
        
        .modal-close:hover {
            background-color: #f1f5f9;
            color: #ef4444;
        }
        
        .modal-body {
            padding: 25px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }

        .form-row {
            display: flex;
            gap: 15px;
            align-items: flex-end;
        }

        .form-row .form-group {
            flex: 1;
            margin-bottom: 0;
        }

        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #374151;
            font-size: 14px;
        }
        
        .form-input, .form-select, .form-textarea {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            font-size: 14px;
            color: #374151;
            transition: all 0.3s ease;
            background-color: #fafbfc;
        }
        
        .form-input:focus, .form-select:focus, .form-textarea:focus {
            outline: none;
            border-color: #0ea5e9;
            box-shadow: 0 0 0 3px rgba(14, 165, 233, 0.1);
            background-color: white;
        }
        
        .form-textarea {
            resize: vertical;
            min-height: 100px;
        }
        
        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        
        .modal-footer {
            padding: 20px 25px;
            border-top: 1px solid #e2e8f0;
            display: flex;
            justify-content: flex-end;
            gap: 12px;
            background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
            border-radius: 0 0 15px 15px;
        }
        
        .btn {
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            border: none;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #0ea5e9 0%, #0284c7 100%);
            color: white;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(14, 165, 233, 0.3);
        }
        
        .btn-secondary {
            background-color: #f1f5f9;
            color: #475569;
            border: 2px solid #e2e8f0;
        }
        
        .btn-secondary:hover {
            background-color: #e2e8f0;
            transform: translateY(-1px);
        }
        
        .btn-danger {
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
            color: white;
        }
        
        .btn-danger:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(239, 68, 68, 0.3);
        }
        
        .dropdown {
            position: relative;
            display: inline-block;
            width: 100%;
        }
        
        .dropdown-toggle {
            width: 100%;
            background-color: #fafbfc;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            padding: 12px 16px;
            cursor: pointer;
            font-size: 14px;
            color: #374151;
            display: flex;
            align-items: center;
            justify-content: space-between;
            transition: all 0.3s ease;
            text-align: left;
        }
        
        .dropdown-toggle:hover {
            background-color: #f1f5f9;
            border-color: #cbd5e1;
        }
        
        .dropdown-menu {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
            z-index: 100;
            margin-top: 5px;
            opacity: 0;
            visibility: hidden;
            transform: translateY(-10px);
            transition: all 0.3s ease;
            border: 1px solid #e2e8f0;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .dropdown.active .dropdown-menu {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }
        
        .dropdown-item {
            display: block;
            width: 100%;
            padding: 12px 16px;
            text-align: left;
            background: none;
            border: none;
            cursor: pointer;
            font-size: 14px;
            color: #374151;
            transition: all 0.3s ease;
        }
        
        .dropdown-item:hover {
            background-color: #f8fafc;
        }
        
        .boxes-container {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
            margin-top: 15px;
        }
        
        .box-card {
            background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
            border-radius: 10px;
            padding: 15px;
            border: 2px solid #e2e8f0;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .box-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }
        
        .box-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }
        
        .box-card.selected {
            background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
            border-color: #0ea5e9;
        }
        
        .box-card.selected::before {
            transform: scaleX(1);
        }
        
        .box-name {
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 8px;
            font-size: 16px;
        }
        
        .box-info {
            font-size: 12px;
            color: #64748b;
            line-height: 1.4;
        }
        
        .box-actions {
            position: absolute;
            top: 8px;
            right: 8px;
            display: flex;
            gap: 5px;
            opacity: 0;
            transition: opacity 0.3s ease;
        }
        
        .box-card:hover .box-actions {
            opacity: 1;
        }
        
        .time-slots {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 10px;
            margin-top: 15px;
        }
        
        .time-slot {
            padding: 10px;
            background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 13px;
            font-weight: 500;
            color: #475569;
        }
        
        .time-slot:hover {
            background-color: #f1f5f9;
            border-color: #cbd5e1;
            transform: translateY(-1px);
        }
        
        .time-slot.selected {
            background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
            border-color: #0ea5e9;
            color: #0284c7;
            font-weight: 700;
        }
        
        .share-buttons {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-top: 20px;
        }
        
        .share-btn {
            padding: 15px;
            border-radius: 10px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            border: none;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
            text-decoration: none;
        }
        
        .share-whatsapp {
            background: linear-gradient(135deg, #25d366 0%, #128c7e 100%);
            color: white;
        }
        
        .share-whatsapp:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(37, 211, 102, 0.3);
        }
        
        .share-email {
            background: linear-gradient(135deg, #ea4335 0%, #c5221f 100%);
            color: white;
        }
        
        .share-email:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(234, 67, 53, 0.3);
        }
        
        .touch-indicator {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 20px;
            border-radius: 50px;
            font-size: 14px;
            font-weight: 600;
            box-shadow: 0 5px 20px rgba(102, 126, 234, 0.4);
            z-index: 100;
            transform: translateY(100px);
            transition: transform 0.3s ease;
        }
        
        .touch-indicator.show {
            transform: translateY(0);
        }
        
        .professionals-management {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }
        
        .professional-form {
            background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
            padding: 20px;
            border-radius: 10px;
            border: 1px solid #e2e8f0;
        }
        
        .professional-list-management {
            display: flex;
            flex-direction: column;
            gap: 10px;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .professional-item-management {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 15px;
            background: white;
            border-radius: 8px;
            border: 1px solid #e2e8f0;
            transition: all 0.3s ease;
        }
        
        .professional-item-management:hover {
            border-color: #cbd5e1;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        }
        
        .professional-info-management {
            display: flex;
            align-items: center;
            gap: 12px;
            flex: 1;
        }
        
        .professional-actions {
            display: flex;
            gap: 8px;
        }
        
        .action-btn {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            border: none;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        
        .action-btn.edit {
            background-color: #fbbf24;
            color: white;
        }
        
        .action-btn.delete {
            background-color: #ef4444;
            color: white;
        }
        
        .action-btn:hover {
            transform: scale(1.1);
        }
        
        .boxes-management {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }
        
        .box-form {
            background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
            padding: 20px;
            border-radius: 10px;
            border: 1px solid #e2e8f0;
        }
        
        .box-list-management {
            display: flex;
            flex-direction: column;
            gap: 10px;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .box-item-management {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 15px;
            background: white;
            border-radius: 8px;
            border: 1px solid #e2e8f0;
            transition: all 0.3s ease;
        }
        
        .box-item-management:hover {
            border-color: #cbd5e1;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        }
        
        .box-info-management {
            flex: 1;
        }
        
        .box-name-management {
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 4px;
        }
        
        .box-details-management {
            font-size: 12px;
            color: #64748b;
        }
        
        .loading {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 40px;
            color: #64748b;
        }
        
        .spinner {
            width: 20px;
            height: 20px;
            border: 2px solid #e2e8f0;
            border-top: 2px solid #0ea5e9;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 10px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        /* Tabs Styles */
        .tabs-navigation {
            display: flex;
            border-bottom: 2px solid #e2e8f0;
            margin-bottom: 25px;
        }
        
        .tab-btn {
            flex: 1;
            padding: 12px 20px;
            background: none;
            border: none;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            color: #64748b;
            transition: all 0.3s ease;
            border-bottom: 3px solid transparent;
        }
        
        .tab-btn:hover {
            background-color: #f8fafc;
            color: #0ea5e9;
        }
        
        .tab-btn.active {
            color: #0ea5e9;
            border-bottom-color: #0ea5e9;
            background-color: #f0f9ff;
        }
        
        .tab-content {
            display: none;
        }
        
        .tab-content.active {
            display: block;
        }
        
        .tab-buttons {
            display: flex;
            gap: 10px;
            justify-content: flex-end;
            margin-top: 20px;
        }

        .radio-group {
            display: flex;
            flex-direction: column;
            gap: 10px;
            margin-top: 5px;
        }

        .radio-label {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 0;
            cursor: pointer;
            font-size: 14px;
        }

        .radio-label input[type="radio"] {
            margin: 0;
        }

        .radio-label:hover {
            background-color: #f8f9fa;
            border-radius: 4px;
            padding-left: 8px;
            padding-right: 8px;
        }

        .radio-label span {
            font-size: 14px;
            color: #374151;
        }

        /* Professional list styles */
        .professional-list {
            display: flex;
            flex-direction: column;
            gap: 8px;
            margin-top: 10px;
            width: 100%;
            min-height: auto;
            max-height: 400px;
            overflow-y: auto;
            overflow-x: hidden;
            padding-right: 5px;
        }
        
        /* Custom scrollbar for professional list */
        .professional-list::-webkit-scrollbar {
            width: 6px;
        }
        
        .professional-list::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 3px;
        }
        
        .professional-list::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 3px;
        }
        
        .professional-list::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }

        .professional-item {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 8px;
            border: 1px solid #e9ecef;
            transition: all 0.2s ease;
        }

        .professional-item:hover {
            background: #e9ecef;
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .professional-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: #007bff;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 12px;
        }

        .professional-info {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 2px;
        }

        .professional-name {
            font-weight: 600;
            font-size: 13px;
            color: #333;
        }

        .professional-specialty {
            font-size: 11px;
            color: #666;
        }

        .professional-status {
            font-size: 12px;
            font-weight: bold;
        }

        .professional-status.active {
            color: #28a745;
        }

        .no-items {
            text-align: center;
            color: #666;
            font-style: italic;
            padding: 20px;
            font-size: 13px;
        }
        
        @media (max-width: 1200px) {
            .main-content {
                grid-template-columns: 1fr;
            }
            
            .sidebar {
                order: -1;
            }
            
            .calendar-header {
                grid-template-columns: repeat(5, 1fr);
            }
            
            .calendar-body {
                grid-template-columns: repeat(5, 1fr);
            }
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 15px;
            }
            
            .header {
                flex-direction: column;
                gap: 15px;
                text-align: center;
            }
            
            .header-actions {
                justify-content: center;
            }
            
            .calendar-navigation {
                flex-direction: column;
                gap: 15px;
            }
            
            .view-controls {
                justify-content: center;
            }
            
            .calendar-header {
                grid-template-columns: repeat(2.5, 1fr);
            }
            
            .calendar-body {
                grid-template-columns: repeat(2.5, 1fr);
            }
            
            .day-column:nth-child(n+3) {
                display: none;
            }
            
            .form-row {
                grid-template-columns: 1fr;
            }
            
            .boxes-container {
                grid-template-columns: 1fr;
            }
            
            .time-slots {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .share-buttons {
                grid-template-columns: 1fr;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
            }
            
            .modal {
                width: 95%;
                margin: 10px;
            }
            
            .professional-item-management {
                flex-direction: column;
                align-items: flex-start;
                gap: 10px;
            }
            
            .box-item-management {
                flex-direction: column;
                align-items: flex-start;
                gap: 10px;
            }
        }
        
        @media (max-width: 480px) {
            .calendar-header {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .calendar-body {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .day-column:nth-child(n+3) {
                display: none;
            }
        }

        /* Box availability styles */
        .box-availability-section {
            margin-bottom: 15px;
        }

        .box-availability-title {
            font-size: 14px;
            font-weight: 600;
            color: #374151;
            margin-bottom: 10px;
            padding-bottom: 5px;
            border-bottom: 1px solid #e5e7eb;
        }

        .box-availability-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 8px;
        }

        .box-availability-item {
            background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
            border: 1px solid #0ea5e9;
            border-radius: 8px;
            padding: 10px;
            font-size: 12px;
            transition: all 0.2s ease;
            min-height: 60px;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .box-availability-item:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(14, 165, 233, 0.15);
        }

        .box-name-availability {
            font-weight: 600;
            color: #0c4a6e;
            margin-bottom: 6px;
            font-size: 13px;
            text-align: center;
        }

        .box-shifts-availability {
            display: flex;
            gap: 4px;
            flex-wrap: wrap;
            justify-content: center;
        }

        .shift-badge {
            background: #0ea5e9;
            color: white;
            padding: 3px 8px;
            border-radius: 12px;
            font-size: 10px;
            font-weight: 600;
        }

        .shift-badge.unavailable {
            background: #ef4444;
            opacity: 0.7;
        }

        .no-boxes-available {
            grid-column: 1 / -1; /* Span across both columns */
            text-align: center;
            color: #64748b;
            font-style: italic;
            padding: 20px;
            font-size: 13px;
            background: #f8fafc;
            border: 1px dashed #cbd5e1;
            border-radius: 8px;
        }

        /* Share button styles */
        .day-share-button {
            position: absolute;
            bottom: 5px;
            right: 5px;
            width: 24px;
            height: 24px;
            background: #0ea5e9;
            color: white;
            border: none;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            transition: all 0.2s ease;
            z-index: 10;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .day-share-button:hover {
            background: #0284c7;
            transform: scale(1.1);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
        }

        .day-column {
            position: relative;
        }

        /* Share modal styles */
        .share-options-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            background-color: rgba(0, 0, 0, 0.5);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 10000;
        }

        .share-options-content {
            background: white;
            border-radius: 12px;
            padding: 24px;
            max-width: 400px;
            width: 90%;
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
        }

        .share-options-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 16px;
            text-align: center;
            color: #374151;
        }

        .share-options-buttons {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .share-option-btn {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px 16px;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            background: white;
            cursor: pointer;
            transition: all 0.2s ease;
            font-size: 14px;
            text-decoration: none;
            color: #374151;
        }

        .share-option-btn:hover {
            background: #f9fafb;
            border-color: #d1d5db;
            transform: translateY(-1px);
        }

        .share-option-icon {
            font-size: 20px;
        }

        /* Responsive design for box availability */
        @media (max-width: 768px) {
            .box-availability-container {
                grid-template-columns: 1fr;
            }
            
            .no-boxes-available {
                grid-column: 1;
            }
            
            /* Header responsive styles for mobile */
            .header {
                margin: -20px -20px 20px -20px; /* Extend to full width by negating container padding */
                padding: 8px 15px; /* Reduced height with smaller padding */
                border-radius: 0; /* Remove border radius for full width */
            }
            
            .header h1 {
                font-size: 14px; /* Even smaller title */
                font-weight: 600;
            }
            
            /* Hide the icon emoji in mobile */
            .header-icon {
                display: none;
            }
            
            .user-info {
                gap: 6px; /* Reduced gap */
            }
            
            .user-avatar {
                width: 24px; /* Even smaller avatar */
                height: 24px;
                font-size: 10px; /* Smaller font in avatar */
                border-width: 1px;
            }
            
            .user-info span {
                font-size: 11px; /* Even smaller username */
                font-weight: 500;
            }
            
            /* Mobile Navigation Bar - visible only on mobile */
            .mobile-nav-bar {
                display: flex;
                justify-content: space-around;
                background: #ffffff; /* White background for contrast */
                border-bottom: 2px solid #cbd5e1;
                border-top: 1px solid #cbd5e1;
                margin: 3px -20px 0 -20px; /* Full width extending beyond container */
                padding: 15px 5px;
                box-shadow: 0 3px 10px rgba(0, 0, 0, 0.15);
                position: relative;
                z-index: 10;
            }
            
            .mobile-nav-btn {
                background: none;
                border: none;
                padding: 10px 8px;
                font-size: 11px;
                font-weight: 700;
                color: #475569;
                cursor: pointer;
                border-radius: 8px;
                transition: all 0.3s ease;
                flex: 1;
                text-align: center;
                min-height: 40px;
                display: flex;
                align-items: center;
                justify-content: center;
            }
            
            .mobile-nav-btn.active {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
            }
            
            .mobile-nav-btn:hover:not(.active) {
                background: #f1f5f9;
                color: #1e293b;
                transform: translateY(-1px);
            }
            
            /* Hide all sections by default on mobile */
            #mobile-calendar-section,
            #mobile-calendar-section-content,
            .mobile-section {
                display: none;
            }
            
            /* Show calendar by default */
            #mobile-calendar-section,
            #mobile-calendar-section-content {
                display: block;
            }
            
            /* Adjust main-content for mobile */
            .main-content {
                grid-template-columns: 1fr;
                gap: 0;
            }
            
            /* Style individual mobile sections */
            .mobile-section {
                background: white;
                border-radius: 12px;
                padding: 20px;
                margin: 15px 0;
                box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
            }
            
            /* Style sidebar sections in desktop mode */
            .sidebar-section {
                background: white;
                border-radius: 12px;
                padding: 20px;
                margin: 15px 0;
                box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
            }
            
            /* Responsive Calendar Navigation - Override desktop styles */
            .calendar-navigation {
                margin-bottom: 10px !important;
                display: flex !important;
                flex-direction: row !important;
                justify-content: center !important;
                align-items: center !important;
                gap: 8px !important;
                width: 100% !important;
                padding: 10px !important;
                background-color: transparent !important;
                box-shadow: none !important;
                flex-wrap: nowrap !important;
            }
            
            .calendar-navigation .nav-button {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
                color: white !important;
                border: none !important;
                border-radius: 6px !important;
                padding: 8px 10px !important;
                cursor: pointer !important;
                font-size: 14px !important;
                font-weight: 600 !important;
                transition: all 0.3s ease !important;
                box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3) !important;
                width: 36px !important;
                height: 36px !important;
                display: flex !important;
                align-items: center !important;
                justify-content: center !important;
                flex-shrink: 0 !important;
                margin: 0 !important;
            }
            
            .calendar-navigation .nav-button:hover {
                transform: translateY(-1px) !important;
                box-shadow: 0 3px 10px rgba(102, 126, 234, 0.4) !important;
            }
            
            .calendar-navigation .current-month {
                padding: 6px 12px !important;
                border-radius: 6px !important;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
                color: white !important;
                font-weight: 600 !important;
                text-align: center !important;
                box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3) !important;
                font-size: 12px !important;
                min-width: 120px !important;
                max-width: 140px !important;
                height: 36px !important;
                display: flex !important;
                align-items: center !important;
                justify-content: center !important;
            }
            
            /* Responsive Horizontal Calendar */
            .calendar-container {
                overflow-x: auto !important;
                overflow-y: auto !important;
                -webkit-overflow-scrolling: touch !important;
                scroll-behavior: smooth !important;
                height: calc(100vh - 200px) !important;
                max-height: calc(100vh - 200px) !important;
                position: relative !important;
            }
            
            .calendar-header {
                display: grid !important;
                grid-template-columns: repeat(5, 1fr) !important;
                min-width: calc(100vw * 1.5) !important;
                width: max-content !important;
                position: sticky !important;
                top: 0 !important;
                z-index: 10 !important;
                background: white !important;
            }
            
            .calendar-header .day-header {
                min-width: calc(30vw) !important;
                width: calc(30vw) !important;
                padding: 6px 3px !important;
                font-size: 9px !important;
                text-align: center !important;
                border-right: 1px solid #e2e8f0 !important;
                border-bottom: 2px solid #cbd5e1 !important;
            }
            
            .calendar-body {
                display: grid !important;
                grid-template-columns: repeat(5, 1fr) !important;
                grid-auto-rows: minmax(120px, auto) !important;
                height: auto !important;
                min-height: auto !important;
                min-width: calc(100vw * 1.5) !important;
                width: max-content !important;
                gap: 0 !important;
                overflow-y: visible !important;
            }
            
            .day-column {
                min-width: calc(30vw) !important;
                width: calc(30vw) !important;
                border-right: 1px solid #e2e8f0 !important;
                border-bottom: 1px solid #e2e8f0 !important;
                margin: 0 !important;
                min-height: 120px !important;
                height: auto !important;
                display: flex !important;
                flex-direction: column !important;
                overflow: visible !important;
            }
            
            .day-date {
                padding: 1px !important;
                font-size: 7px !important;
            }
            
            .appointments-container {
                padding: 1px !important;
                min-height: 30px !important;
            }
            
            .appointment {
                padding: 2px !important;
                margin-bottom: 1px !important;
                font-size: 7px !important;
            }
            
            .appointment-time {
                font-size: 6px !important;
                margin-bottom: 0px !important;
            }
            
            .appointment-patient {
                font-size: 5px !important;
                margin-bottom: 1px !important;
            }
            
            .appointment-professional {
                font-size: 6px !important;
            }
            

            
            /* Responsive Add Appointment Button */
            .day-column .add-appointment-btn,
            .appointments-container .add-appointment-btn {
                display: flex !important;
                flex-direction: column !important;
                align-items: center !important;
                justify-content: center !important;
                padding: 8px 4px !important;
                font-size: 8px !important;
                min-height: 50px !important;
                width: 90% !important;
                margin: 5px auto !important;
                border-radius: 8px !important;
                background: linear-gradient(135deg, #0ea5e9 0%, #06b6d4 100%) !important;
                color: white !important;
                border: none !important;
                cursor: pointer !important;
                transition: all 0.3s ease !important;
                box-shadow: 0 2px 8px rgba(14, 165, 233, 0.3) !important;
                text-align: center !important;
                line-height: 1.2 !important;
            }
            
            .day-column .add-appointment-btn:hover,
            .appointments-container .add-appointment-btn:hover {
                transform: translateY(-1px) !important;
                box-shadow: 0 4px 12px rgba(14, 165, 233, 0.4) !important;
                background: linear-gradient(135deg, #0284c7 0%, #0891b2 100%) !important;
            }
            
            .add-btn-line1 {
                font-size: 16px !important;
                font-weight: bold !important;
                line-height: 1 !important;
                margin-bottom: 2px !important;
            }
            
            .add-btn-line2 {
                font-size: 7px !important;
                font-weight: 600 !important;
                text-transform: uppercase !important;
                letter-spacing: 0.5px !important;
                line-height: 1 !important;
            }

        }
        
        /* Hide mobile nav and mobile sections on desktop - outside media query */
        .mobile-nav-bar,
        .mobile-section {
            display: none;
        }
        
        /* Show mobile nav only on mobile devices */
        /* Login Modal Styles */
        .login-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 10000;
            opacity: 1;
            visibility: visible;
        }
        
        .login-modal {
            background: white;
            border-radius: 20px;
            padding: 40px;
            width: 90%;
            max-width: 400px;
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.4);
            transform: scale(1);
            transition: all 0.3s ease;
            text-align: center;
        }
        
        .login-title {
            font-size: 28px;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 10px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .login-subtitle {
            font-size: 16px;
            color: #64748b;
            margin-bottom: 30px;
        }
        
        .login-form {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }
        
        .login-input-group {
            position: relative;
        }
        
        .login-input {
            width: 100%;
            padding: 15px 20px;
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            font-size: 16px;
            color: #374151;
            background-color: #fafbfc;
            transition: all 0.3s ease;
            box-sizing: border-box;
        }
        
        .login-input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
            background-color: white;
        }
        
        .login-input::placeholder {
            color: #9ca3af;
        }
        
        .login-btn {
            padding: 15px 30px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: 10px;
        }
        
        .login-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
        }
        
        .login-btn:active {
            transform: translateY(0);
        }
        
        .login-icon {
            font-size: 48px;
            margin-bottom: 20px;
        }
        
        .main-content-hidden {
            filter: blur(5px);
            pointer-events: none;
            opacity: 0.3;
        }
        
        /* User Menu Dropdown Styles */
        .user-menu {
            position: relative;
            display: inline-block;
        }
        
        .user-info {
            cursor: pointer;
            transition: all 0.3s ease;
            border-radius: 25px;
            padding: 5px;
        }
        
        .user-info:hover {
            background-color: rgba(255, 255, 255, 0.1);
        }
        
        .user-dropdown {
            position: absolute;
            top: 100%;
            right: 0;
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            min-width: 180px;
            opacity: 0;
            visibility: hidden;
            transform: translateY(-10px);
            transition: all 0.3s ease;
            z-index: 1000;
            margin-top: 10px;
            border: 1px solid #e2e8f0;
        }
        
        .user-dropdown.show {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }
        
        .user-dropdown-header {
            padding: 15px;
            border-bottom: 1px solid #e2e8f0;
            background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
            border-radius: 12px 12px 0 0;
        }
        
        .user-dropdown-name {
            font-weight: 600;
            color: #1e293b;
            font-size: 14px;
            margin-bottom: 2px;
        }
        
        .user-dropdown-role {
            font-size: 12px;
            color: #64748b;
        }
        
        .user-dropdown-menu {
            padding: 8px 0;
        }
        
        .user-dropdown-item {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 12px 15px;
            color: #374151;
            text-decoration: none;
            font-size: 14px;
            transition: all 0.2s ease;
            cursor: pointer;
            border: none;
            background: none;
            width: 100%;
            text-align: left;
        }
        
        .user-dropdown-item:hover {
            background-color: #f8fafc;
            color: #1e293b;
        }
        
        .user-dropdown-item.logout {
            color: #ef4444;
            border-top: 1px solid #e2e8f0;
        }
        
        .user-dropdown-item.logout:hover {
            background-color: #fef2f2;
            color: #dc2626;
        }
        
        .dropdown-icon {
            font-size: 16px;
            width: 16px;
            text-align: center;
        }

        @media (max-width: 768px) {
            .login-modal {
                padding: 30px 25px;
                width: 95%;
                max-width: 350px;
            }
            
            .login-title {
                font-size: 24px;
            }
            
            .login-subtitle {
                font-size: 14px;
            }
            
            .login-input {
                padding: 12px 16px;
                font-size: 14px;
            }
            
            .login-btn {
                padding: 12px 24px;
                font-size: 14px;
            }
        }
        
        @media (max-width: 768px) {
            /* Hide the mobile navigation bar in responsive mode */
            .mobile-nav-bar {
                display: none !important;
            }
            
            /* Show the calendar navigation bar in responsive mode */
            .calendar-navigation {
                display: flex !important;
                justify-content: space-between !important;
                align-items: center !important;
                margin-bottom: 15px !important;
                padding: 15px !important;
                background-color: #fff !important;
                border-radius: 12px !important;
                box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05) !important;
            }
            
            /* Hide sidebar sections when calendar is active in responsive mode */
            .sidebar {
                display: none !important;
            }
            
            /* Hide boxes sections in responsive mode but keep functionality */
            .mobile-section#mobile-boxes-section {
                display: none !important;
            }
            
            /* Show only calendar content by default in responsive */
            .calendar-container {
                display: block !important;
            }
            
            /* Hide mobile sections for professionals and KPIs in responsive mode */
            .mobile-section#mobile-professionals-section,
            .mobile-section#mobile-kpis-section {
                display: none !important;
            }
            
            /* Adjust main content layout for responsive */
            .main-content {
                display: block !important;
                gap: 0 !important;
            }
            
            /* Ensure calendar takes full width in responsive */
            .calendar-container {
                width: 100% !important;
                margin-bottom: 20px !important;
            }
        }
    </style>
</head>
<body>
    <!-- Login Modal -->
    <div class="login-overlay" id="loginOverlay">
        <div class="login-modal">
            <div class="login-icon">🏥</div>
            <h2 class="login-title">Bienvenido</h2>
            <p class="login-subtitle">Ingresa tus credenciales para acceder al sistema</p>
            <form class="login-form" id="loginForm">
                <div class="login-input-group">
                    <input type="text" class="login-input" id="username" placeholder="Usuario" required>
                </div>
                <div class="login-input-group">
                    <input type="password" class="login-input" id="password" placeholder="Contraseña" required>
                </div>
                <button type="submit" class="login-btn">Iniciar Sesión</button>
            </form>
        </div>
    </div>
    
    <div class="container main-content-hidden" id="mainContent">
        <div class="header">
            <h1><span class="header-icon">🏥</span> Calendario de Agendamiento Médico</h1>
            <div class="header-actions">
                <div class="user-menu">
                    <div class="user-info" id="userInfo">
                        <div class="user-avatar">U</div>
                        <span id="userNameDisplay">Dr. Usuario</span>
                    </div>
                    <div class="user-dropdown" id="userDropdown">
                        <div class="user-dropdown-header">
                            <div class="user-dropdown-name" id="dropdownUserName">Dr. Usuario</div>
                            <div class="user-dropdown-role">Administrador</div>
                        </div>
                        <div class="user-dropdown-menu">
                            <button class="user-dropdown-item logout" id="logoutBtn">
                                <span class="dropdown-icon">🚪</span>
                                Cerrar Sesión
                            </button>
                        </div>
                    </div>
                </div>

            </div>
        </div>
        
        <!-- Mobile Navigation Bar -->
        <div class="mobile-nav-bar">
            <button class="mobile-nav-btn active" data-section="calendar">Calendario</button>
            <button class="mobile-nav-btn" data-section="professionals">+ Profesionales</button>
            <button class="mobile-nav-btn" data-section="boxes">+ Boxes</button>
            <button class="mobile-nav-btn" data-section="kpis">KPIs</button>
        </div>
        
        <div class="calendar-navigation" id="mobile-calendar-section">
            <button class="nav-button" id="prevMonth">←</button>
            <div class="current-month" id="currentMonth">Mayo 2023</div>
            <button class="nav-button" id="nextMonth">→</button>
        </div>
        
        <div class="main-content" id="mobile-calendar-section-content">
            <div class="calendar-container">
                <div class="calendar-header">
                    <div class="day-header">Lunes</div>
                    <div class="day-header">Martes</div>
                    <div class="day-header">Miércoles</div>
                    <div class="day-header">Jueves</div>
                    <div class="day-header">Viernes</div>
                </div>
                <div class="calendar-body" id="calendarBody">
                    <!-- Calendar days will be generated by JavaScript -->
                </div>
            </div>
            
            <div class="sidebar">
                <div class="sidebar-section">
                    <div class="sidebar-title">
                        Profesionales
                        <button class="btn btn-primary" style="padding: 5px 10px; font-size: 12px;" onclick="openProfessionalsModal()">
                            +
                        </button>
                    </div>
                    <div class="professional-list" id="professionalList">
                        <!-- Professionals will be added by JavaScript -->
                    </div>
                </div>
                
                <div class="sidebar-section">
                    <div class="sidebar-title">
                        Boxes
                        <button class="btn btn-primary" style="padding: 5px 10px; font-size: 12px;" onclick="openBoxManagementModal()">
                            +
                        </button>
                    </div>
                    <div class="box-availability-section">
                        <div class="box-availability-title">Boxes disponibles</div>
                        <div class="box-availability-container" id="boxAvailabilityContainer">
                            <!-- Box availability will be generated by JavaScript -->
                        </div>
                    </div>
                    <div class="stats-grid" style="margin-top: 15px;">
                        <div class="stat-card">
                            <div class="stat-number" id="totalBoxes">4</div>
                            <div class="stat-label">Total Boxes</div>
                        </div>
                    </div>
                </div>
                
                <div class="sidebar-section">
                    <div class="sidebar-title">KPIs por Profesional</div>
                    <div class="professional-kpis-container" id="professionalKPIs">
                        <!-- Professional KPIs will be generated by JavaScript -->
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Mobile Individual Sections (outside main-content for independent display) -->
        <div class="mobile-section" id="mobile-professionals-section">
            <div class="sidebar-title">
                Profesionales
                <button class="btn btn-primary" style="padding: 5px 10px; font-size: 12px;" onclick="openProfessionalsModal()">
                    +
                </button>
            </div>
            <div class="professional-list" id="professionalListMobile">
                <!-- Professionals will be added by JavaScript -->
            </div>
        </div>
        
        <div class="mobile-section" id="mobile-boxes-section">
            <div class="sidebar-title">
                Boxes
                <button class="btn btn-primary" style="padding: 5px 10px; font-size: 12px;" onclick="openBoxManagementModal()">
                    +
                </button>
            </div>
            <div class="box-availability-section">
                <div class="box-availability-title">Boxes disponibles</div>
                <div class="box-availability-container" id="boxAvailabilityContainerMobile">
                    <!-- Box availability will be generated by JavaScript -->
                </div>
            </div>
            <div class="stats-grid" style="margin-top: 15px;">
                <div class="stat-card">
                    <div class="stat-number" id="totalBoxesMobile">4</div>
                    <div class="stat-label">Total Boxes</div>
                </div>
            </div>
        </div>
        
        <div class="mobile-section" id="mobile-kpis-section">
            <div class="sidebar-title">KPIs por Profesional</div>
            <div class="professional-kpis-container" id="professionalKPIsMobile">
                <!-- Professional KPIs will be generated by JavaScript -->
            </div>
        </div>
    </div>
    

    
    <!-- Appointment Modal -->
    <div class="modal-overlay" id="appointmentModal">
        <div class="modal">
            <div class="modal-header">
                <h2 class="modal-title">📅 Agendar Turno</h2>
                <button class="modal-close" id="closeAppointmentModal">&times;</button>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label class="form-label">Día</label>
                    <input type="text" class="form-input" id="appointmentDate" readonly>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label">Turnos</label>
                        <select class="form-select" id="appointmentShift">
                            <option value="">Seleccionar turno</option>
                            <option value="AM">AM</option>
                            <option value="PM">PM</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Profesional</label>
                        <div class="dropdown" id="professionalDropdown">
                            <button class="dropdown-toggle" type="button">
                                Seleccionar profesional
                                <span>▼</span>
                            </button>
                            <div class="dropdown-menu" id="professionalMenu">
                                <!-- Professionals will be added by JavaScript -->
                            </div>
                        </div>
                    </div>
                </div>
                <div class="form-group">
                    <label class="form-label">Box</label>
                    <div class="boxes-container" id="boxesContainer">
                        <!-- Boxes will be added by JavaScript -->
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" id="cancelAppointment">Cancelar</button>
                <button class="btn btn-primary" id="saveAppointment">💾 Guardar Turno</button>
            </div>
        </div>
    </div>
    
    <!-- Appointment Details Modal -->
    <div class="modal-overlay" id="appointmentDetailsModal">
        <div class="modal">
            <div class="modal-header">
                <h2 class="modal-title">📋 Detalle de Turno</h2>
                <button class="modal-close" id="closeDetailsModal">&times;</button>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label class="form-label">Día</label>
                    <input type="text" class="form-input" id="detailsDate" readonly>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label">Turnos</label>
                        <select class="form-select" id="detailsShift" disabled>
                            <option value="">Seleccionar turno</option>
                            <option value="AM">AM</option>
                            <option value="PM">PM</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Profesional</label>
                        <div class="dropdown" id="detailsProfessionalDropdown">
                            <button class="dropdown-toggle" type="button" disabled>
                                Seleccionar profesional
                                <span>▼</span>
                            </button>
                            <div class="dropdown-menu" id="detailsProfessionalMenu">
                                <!-- Professionals will be added by JavaScript -->
                            </div>
                        </div>
                    </div>
                </div>
                <div class="form-group">
                    <label class="form-label">Box</label>
                    <div class="boxes-container" id="detailsBoxesContainer">
                        <!-- Boxes will be added by JavaScript -->
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-danger" id="deleteAppointment">🗑️ Eliminar</button>
                <button class="btn btn-secondary" id="editAppointment">✏️ Editar</button>
                <button class="btn btn-primary" id="closeDetails">✅ Cerrar</button>
            </div>
        </div>
    </div>
    
    <!-- Professionals Management Modal -->
    <div class="modal-overlay" id="professionalsModal" onclick="if(event.target === this) closeProfessionalsModal()">
        <div class="modal">
            <div class="modal-header">
                <h2 class="modal-title">👨‍⚕️ Gestión de Profesionales</h2>
                <button class="modal-close" id="closeProfessionalsModal" onclick="closeProfessionalsModal()">&times;</button>
            </div>
            <div class="modal-body">
                <!-- Tabs Navigation -->
                <div class="tabs-navigation">
                    <button class="tab-btn active" data-tab="add-professional" onclick="switchProfessionalTab('add-professional')">Agregar Profesional</button>
                    <button class="tab-btn" data-tab="modify-status" onclick="switchProfessionalTab('modify-status')">Modificar Estado</button>
                    <button class="tab-btn" data-tab="delete-professional" onclick="switchProfessionalTab('delete-professional')">Eliminar Profesional</button>
                </div>
                
                <!-- Add Professional Tab -->
                <div class="tab-content active" id="add-professional">
                    <div class="form-group">
                        <label class="form-label">Nombre del Profesional</label>
                        <input type="text" class="form-input" id="newProfessionalName" placeholder="Ej: Dr. Juan Pérez">
                    </div>
                    <div class="tab-buttons">
                        <button class="btn btn-secondary" onclick="closeProfessionalsModal()">Cancelar</button>
                        <button class="btn btn-primary" onclick="addNewProfessional()">Agregar</button>
                    </div>
                </div>
                
                <!-- Modify Professional Status Tab -->
                <div class="tab-content" id="modify-status">
                    <div class="form-group">
                        <label class="form-label">Seleccionar Profesional</label>
                        <select class="form-select" id="modifyStatusProfessionalSelect" onchange="enableStatusSelect()">
                            <option value="">Seleccionar profesional</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">Estado</label>
                        <select class="form-select" id="modifyStatusSelect" disabled onchange="handleStatusChange()">
                            <option value="">Seleccionar estado</option>
                            <option value="Vigente">Vigente</option>
                            <option value="Vacaciones">Vacaciones</option>
                            <option value="Licencia">Licencia</option>
                            <option value="Permiso">Permiso</option>
                            <option value="Cursos">Cursos</option>
                        </select>
                    </div>
                    
                    <div class="form-group" id="dateRangeGroup" style="display: none;">
                        <label class="form-label">Rango de Fechas</label>
                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label">Desde</label>
                                <input type="date" class="form-input" id="statusFromDate">
                            </div>
                            <div class="form-group">
                                <label class="form-label">Hasta</label>
                                <input type="date" class="form-input" id="statusToDate">
                            </div>
                        </div>
                    </div>
                    
                    <div class="tab-buttons">
                        <button class="btn btn-secondary" onclick="closeProfessionalsModal()">Cancelar</button>
                        <button class="btn btn-primary" onclick="modifyProfessionalStatus()">Aceptar</button>
                    </div>
                </div>
                
                <!-- Delete Professional Tab -->
                <div class="tab-content" id="delete-professional">
                    <div class="form-group">
                        <label class="form-label">Seleccionar Profesional</label>
                        <select class="form-select" id="deleteProfessionalSelect">
                            <option value="">Seleccionar profesional a eliminar</option>
                        </select>
                    </div>

                    
                    <div class="tab-buttons">
                        <button class="btn btn-secondary" onclick="closeProfessionalsModal()">Cancelar</button>
                        <button class="btn btn-danger" onclick="deleteSelectedProfessional()">Eliminar</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Box Management Modal -->
    <div class="modal-overlay" id="boxManagementModal" onclick="if(event.target === this) closeBoxModal()">
        <div class="modal">
            <div class="modal-header">
                <h2 class="modal-title">🏥 Gestión de Boxes</h2>
                <button class="modal-close" id="closeBoxManagementModal" onclick="closeBoxModal()">&times;</button>
            </div>
            <div class="modal-body">
                <!-- Tabs Navigation -->
                <div class="tabs-navigation">
                    <button class="tab-btn active" data-tab="add-box" onclick="switchTab('add-box')">Agregar Box</button>
                    <button class="tab-btn" data-tab="modify-box" onclick="switchTab('modify-box')">Modificar Box</button>
                    <button class="tab-btn" data-tab="delete-box" onclick="switchTab('delete-box')">Eliminar Box</button>
                </div>
                
                <!-- Add Box Tab -->
                <div class="tab-content active" id="add-box">
                    <div class="form-group">
                        <label class="form-label">Nombre del Box</label>
                        <input type="text" class="form-input" id="newBoxName" placeholder="Ej: Box 5">
                    </div>
                    <div class="tab-buttons">
                        <button class="btn btn-secondary" onclick="closeBoxModal()">Cancelar</button>
                        <button class="btn btn-primary" onclick="addNewBox()">Agregar</button>
                    </div>
                </div>
                
                <!-- Modify Box Tab -->
                <div class="tab-content" id="modify-box">
                    <div class="form-group">
                        <label class="form-label">Seleccionar Box</label>
                        <select class="form-select" id="modifyBoxSelect">
                            <option value="">Seleccionar box a modificar</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Nuevo Nombre</label>
                        <input type="text" class="form-input" id="modifyBoxName" placeholder="Nuevo nombre del box">
                    </div>
                    <div class="tab-buttons">
                        <button class="btn btn-secondary" onclick="closeBoxModal()">Cancelar</button>
                        <button class="btn btn-primary" onclick="modifySelectedBox()">Modificar</button>
                    </div>
                </div>
                
                <!-- Delete Box Tab -->
                <div class="tab-content" id="delete-box">
                    <div class="form-group">
                        <label class="form-label">Seleccionar Box</label>
                        <select class="form-select" id="deleteBoxSelect">
                            <option value="">Seleccionar box a eliminar</option>
                        </select>
                    </div>
                    <div class="tab-buttons">
                        <button class="btn btn-secondary" onclick="closeBoxModal()">Cancelar</button>
                        <button class="btn btn-danger" onclick="deleteSelectedBox()">Eliminar</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Settings Modal -->
    <div class="modal-overlay" id="settingsModal">
        <div class="modal">
            <div class="modal-header">
                <h2 class="modal-title">⚙️ Configuración</h2>
                <button class="modal-close" id="closeSettingsModal">&times;</button>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label class="form-label">Duración por Defecto de Citas</label>
                    <select class="form-select" id="defaultDuration">
                        <option value="15">15 minutos</option>
                        <option value="30" selected>30 minutos</option>
                        <option value="45">45 minutos</option>
                        <option value="60">60 minutos</option>
                    </select>
                </div>
                <div class="form-group">
                    <label class="form-label">Hora de Inicio de Jornada</label>
                    <input type="time" class="form-input" id="startTime" value="08:00">
                </div>
                <div class="form-group">
                    <label class="form-label">Hora de Fin de Jornada</label>
                    <input type="time" class="form-input" id="endTime" value="18:00">
                </div>
                <div class="form-group">
                    <label class="form-label">Notificaciones</label>
                    <div style="display: flex; flex-direction: column; gap: 10px;">
                        <label style="display: flex; align-items: center; gap: 10px;">
                            <input type="checkbox" checked> Notificar nuevas citas
                        </label>
                        <label style="display: flex; align-items: center; gap: 10px;">
                            <input type="checkbox" checked> Recordatorios de citas
                        </label>
                        <label style="display: flex; align-items: center; gap: 10px;">
                            <input type="checkbox"> Notificaciones de cancelación
                        </label>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" id="closeSettings">Cerrar</button>
                <button class="btn btn-primary" id="saveSettings">💾 Guardar</button>
            </div>
        </div>
    </div>
    
    <!-- Box Edit Modal -->
    <div class="modal-overlay" id="boxEditModal">
        <div class="modal">
            <div class="modal-header">
                <h2 class="modal-title">🏥 Editar Box</h2>
                <button class="modal-close" id="closeBoxEditModal">&times;</button>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label class="form-label">Nombre del Box</label>
                    <input type="text" class="form-input" id="editBoxName" placeholder="Ej: Box 1">
                </div>
                <div class="form-group">
                    <label class="form-label">Ubicación</label>
                    <input type="text" class="form-input" id="editBoxLocation" placeholder="Ej: Primer piso">
                </div>
                <div class="form-group">
                    <label class="form-label">Equipamiento</label>
                    <input type="text" class="form-input" id="editBoxEquipment" placeholder="Ej: ECG, Tensiómetro">
                </div>
                <div class="form-group">
                    <label class="form-label">Estado</label>
                    <select class="form-select" id="editBoxStatus">
                        <option value="available">Disponible</option>
                        <option value="occupied">Ocupado</option>
                    </select>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" id="cancelBoxEdit">Cancelar</button>
                <button class="btn btn-primary" id="saveBoxEdit">💾 Guardar Cambios</button>
            </div>
        </div>
    </div>
    
    <!-- Touch Indicator -->
    <div class="touch-indicator" id="touchIndicator">
        👆 Desliza para navegar
    </div>

    <!-- Share Options Modal -->
    <div class="share-options-modal" id="shareOptionsModal">
        <div class="share-options-content">
            <div class="share-options-title">📤 Compartir Agenda del Día</div>
            <div class="share-options-buttons">
                <button class="share-option-btn" onclick="shareViaWhatsApp()">
                    <span class="share-option-icon">📱</span>
                    <div>
                        <div style="font-weight: 600;">WhatsApp</div>
                        <div style="font-size: 12px; color: #6b7280;">Compartir por WhatsApp</div>
                    </div>
                </button>
                <button class="share-option-btn" onclick="shareViaEmail()">
                    <span class="share-option-icon">📧</span>
                    <div>
                        <div style="font-weight: 600;">Correo Electrónico</div>
                        <div style="font-size: 12px; color: #6b7280;">Enviar por email</div>
                    </div>
                </button>
                <button class="share-option-btn" onclick="closeShareModal()" style="margin-top: 8px; border-color: #dc2626; color: #dc2626;">
                    <span class="share-option-icon">❌</span>
                    <div>
                        <div style="font-weight: 600;">Cancelar</div>
                    </div>
                </button>
            </div>
        </div>
    </div>



    <script>
        // State management

        // === Backend API Helpers para Profesionales ===
        async function apiRequest(url, options = {}) {
            try {
                const resp = await fetch(url, {
                    credentials: 'same-origin',
                    headers: {
                        'Accept': 'application/json'
                    },
                    ...options
                });
                const data = await resp.json();
                if (!resp.ok || data.success === false) {
                    const msg = data && (data.error || data.detail) ? (data.error || data.detail) : 'Error en la solicitud';
                    throw new Error(msg);
                }
                return data;
            } catch (e) {
                console.error('API error:', e);
                alert('Error: ' + e.message);
                throw e;
            }
        }

        function mapProfessionalRow(row) {
            return {
                id: row.id,
                name: row.nombre,
                email: '',
                phone: '',
                avatar: '👨‍⚕️',
                status: row.estado,
                statusFromDate: row.estado_desde || '',
                statusToDate: row.estado_hasta || ''
            };
        }

        async function loadProfessionalsFromDB() {
            try {
                const res = await apiRequest('api/professionals.php');
                const rows = Array.isArray(res.data) ? res.data : [];
                state.professionals = rows.map(mapProfessionalRow);
                // Refrescar UI dependiente
                displayProfessionalList();
                populateProfessionals();
                populateDetailsProfessionals();
                populateProfessionalSelects();
                populateProfessionalManagement();
                updateStats();
                renderCalendar();
            } catch (_) {
                // ya se mostró alerta en apiRequest
            }
        }
        const state = {
            currentDate: new Date(),
            appointments: [],
            professionals: [],
            boxes: [
                { id: 1, name: 'Box 1', location: 'Primer piso', equipment: 'ECG, Tensiómetro', status: 'available' },
                { id: 2, name: 'Box 2', location: 'Primer piso', equipment: 'Báscula, Tallímetro', status: 'available' },
                { id: 3, name: 'Box 3', location: 'Segundo piso', equipment: 'Ecógrafo', status: 'available' },
                { id: 4, name: 'Box 4', location: 'Segundo piso', equipment: 'Mesa de exploración', status: 'occupied' }
            ],
            selectedAppointment: null,
            selectedProfessional: null,
            selectedBox: null,
            currentView: 'week',
            touchStartX: 0,
            touchEndX: 0,
            settings: {
                defaultDuration: 30,
                startTime: '08:00',
                endTime: '18:00',
                notifications: {
                    newAppointments: true,
                    reminders: true,
                    cancellations: false
                }
            }
        };

        // DOM elements
        const elements = {
            currentMonth: document.getElementById('currentMonth'),
            calendarBody: document.getElementById('calendarBody'),
            prevMonth: document.getElementById('prevMonth'),
            nextMonth: document.getElementById('nextMonth'),
            appointmentModal: document.getElementById('appointmentModal'),
            appointmentDetailsModal: document.getElementById('appointmentDetailsModal'),
            professionalsModal: document.getElementById('professionalsModal'),
            boxesModal: document.getElementById('boxesModal'),
            settingsModal: document.getElementById('settingsModal'),
            boxEditModal: document.getElementById('boxEditModal'),
            closeAppointmentModal: document.getElementById('closeAppointmentModal'),
            closeDetailsModal: document.getElementById('closeDetailsModal'),
            closeProfessionalsModal: document.getElementById('closeProfessionalsModal'),
            closeBoxesModal: document.getElementById('closeBoxesModal'),
            closeSettingsModal: document.getElementById('closeSettingsModal'),
            closeBoxEditModal: document.getElementById('closeBoxEditModal'),
            cancelAppointment: document.getElementById('cancelAppointment'),
            saveAppointment: document.getElementById('saveAppointment'),
            closeDetails: document.getElementById('closeDetails'),
            deleteAppointment: document.getElementById('deleteAppointment'),
            editAppointment: document.getElementById('editAppointment'),
            closeProfessionals: document.getElementById('closeProfessionals'),
            closeBoxes: document.getElementById('closeBoxes'),
            closeSettings: document.getElementById('closeSettings'),
            saveSettings: document.getElementById('saveSettings'),
            cancelBoxEdit: document.getElementById('cancelBoxEdit'),
            saveBoxEdit: document.getElementById('saveBoxEdit'),
            appointmentDate: document.getElementById('appointmentDate'),
            appointmentShift: document.getElementById('appointmentShift'),
            professionalDropdown: document.getElementById('professionalDropdown'),
            professionalMenu: document.getElementById('professionalMenu'),
            boxesContainer: document.getElementById('boxesContainer'),
            professionalList: document.getElementById('professionalList'),
            editBoxName: document.getElementById('editBoxName'),
            editBoxLocation: document.getElementById('editBoxLocation'),
            editBoxEquipment: document.getElementById('editBoxEquipment'),
            editBoxStatus: document.getElementById('editBoxStatus'),
            detailsDate: document.getElementById('detailsDate'),
            detailsStartTime: document.getElementById('detailsStartTime'),
            detailsEndTime: document.getElementById('detailsEndTime'),
            detailsPatient: document.getElementById('detailsPatient'),
            detailsReason: document.getElementById('detailsReason'),
            detailsProfessional: document.getElementById('detailsProfessional'),
            detailsBox: document.getElementById('detailsBox'),
            detailsType: document.getElementById('detailsType'),
            shareWhatsapp: document.getElementById('shareWhatsapp'),
            shareEmail: document.getElementById('shareEmail'),
            touchIndicator: document.getElementById('touchIndicator'),
            professionalListManagement: document.getElementById('professionalListManagement'),
            boxListManagement: document.getElementById('boxListManagement'),
            totalBoxes: document.getElementById('totalBoxes'),
            availableBoxes: document.getElementById('availableBoxes'),
            totalAppointments: document.getElementById('totalAppointments'),
            completedAppointments: document.getElementById('completedAppointments'),
            pendingAppointments: document.getElementById('pendingAppointments'),
            cancelledAppointments: document.getElementById('cancelledAppointments')
        };

        // Initialize calendar
        function initCalendar() {
            renderCalendar();
            setupEventListeners();
            // Cargar profesionales desde backend y luego poblar UI
            loadProfessionalsFromDB();
            populateBoxes();
            populateBoxManagement();
            loadSampleAppointments();
            updateStats();
            setupTouchGestures();
            
            // Ensure professional list is displayed after DOM is fully ready
            setTimeout(() => {
                displayProfessionalList();
            }, 100);
        }

        // Setup event listeners
        function setupEventListeners() {
            // Navigation buttons
            if (elements.prevMonth) {
                elements.prevMonth.addEventListener('click', () => {
                    state.currentDate.setMonth(state.currentDate.getMonth() - 1);
                    renderCalendar();
                });
            }

            if (elements.nextMonth) {
                elements.nextMonth.addEventListener('click', () => {
                    state.currentDate.setMonth(state.currentDate.getMonth() + 1);
                    renderCalendar();
                });
            }

            // Modal close buttons - with null checks
            if (elements.closeAppointmentModal) {
                elements.closeAppointmentModal.addEventListener('click', () => closeModal('appointmentModal'));
            }
            if (elements.cancelAppointment) {
                elements.cancelAppointment.addEventListener('click', () => closeModal('appointmentModal'));
            }
            if (elements.closeDetailsModal) {
                elements.closeDetailsModal.addEventListener('click', () => closeModal('appointmentDetailsModal'));
            }
            if (elements.closeDetails) {
                elements.closeDetails.addEventListener('click', () => closeModal('appointmentDetailsModal'));
            }
            if (elements.closeProfessionalsModal) {
                elements.closeProfessionalsModal.addEventListener('click', () => closeModal('professionalsModal'));
            }
            if (elements.closeProfessionals) {
                elements.closeProfessionals.addEventListener('click', () => closeModal('professionalsModal'));
            }
            if (elements.closeBoxesModal) {
                elements.closeBoxesModal.addEventListener('click', () => closeModal('boxesModal'));
            }
            if (elements.closeBoxes) {
                elements.closeBoxes.addEventListener('click', () => closeModal('boxesModal'));
            }
            if (elements.closeSettingsModal) {
                elements.closeSettingsModal.addEventListener('click', () => closeModal('settingsModal'));
            }
            if (elements.closeSettings) {
                elements.closeSettings.addEventListener('click', () => closeModal('settingsModal'));
            }
            if (elements.closeBoxEditModal) {
                elements.closeBoxEditModal.addEventListener('click', () => closeModal('boxEditModal'));
            }
            if (elements.cancelBoxEdit) {
                elements.cancelBoxEdit.addEventListener('click', () => closeModal('boxEditModal'));
            }
            
            // Box Management Modal close button - with more robust approach
            setTimeout(() => {
                const closeBoxBtn = document.getElementById('closeBoxManagementModal');
                if (closeBoxBtn) {
                    closeBoxBtn.addEventListener('click', (e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        console.log('Close box modal button clicked'); // Debug log
                        closeModal('boxManagementModal');
                    });
                }
                
                // Close modal when clicking outside of it
                const boxModal = document.getElementById('boxManagementModal');
                if (boxModal) {
                    boxModal.addEventListener('click', (e) => {
                        console.log('Box modal overlay clicked', e.target.id); // Debug log
                        if (e.target === boxModal) { // More specific check
                            closeModal('boxManagementModal');
                        }
                    });
                }
            }, 100);

            // Professionals Modal close button and outside click
            document.getElementById('closeProfessionalsModal').addEventListener('click', () => closeModal('professionalsModal'));
            
            // Close professionals modal when clicking outside of it
            document.getElementById('professionalsModal').addEventListener('click', (e) => {
                if (e.target.id === 'professionalsModal') {
                    closeModal('professionalsModal');
                }
            });


            
            // Tab navigation for Box Management Modal (setup after DOM is ready)
            setTimeout(() => {
                document.querySelectorAll('#boxManagementModal .tab-btn').forEach(btn => {
                    btn.addEventListener('click', (e) => {
                        e.preventDefault();
                        const tabId = e.target.getAttribute('data-tab');
                        console.log('Tab clicked:', tabId); // Debug log
                        switchTab(tabId);
                    });
                });
            }, 100);
            
            // Action buttons - with null checks
            console.log('Setting up saveAppointment event listener'); // Debug log
            console.log('saveAppointment element:', elements.saveAppointment); // Debug log
            if (elements.saveAppointment) {
                elements.saveAppointment.addEventListener('click', function(e) {
                    console.log('Save appointment button clicked!'); // Debug log
                    e.preventDefault();
                    saveAppointmentHandler();
                });
            } else {
                console.error('saveAppointment element not found!'); // Debug log
            }
            
            if (elements.deleteAppointment) {
                elements.deleteAppointment.addEventListener('click', deleteAppointmentHandler);
            }
            if (elements.editAppointment) {
                elements.editAppointment.addEventListener('click', editAppointmentHandler);
            }
            if (elements.saveSettings) {
                elements.saveSettings.addEventListener('click', saveSettingsHandler);
            }
            if (elements.saveBoxEdit) {
                elements.saveBoxEdit.addEventListener('click', saveBoxEditHandler);
            }
            
            // Dropdowns
            if (elements.professionalDropdown) {
                elements.professionalDropdown.addEventListener('click', () => {
                    elements.professionalDropdown.classList.toggle('active');
                });
            }
            
            // Share buttons
            if (elements.shareWhatsapp) {
                elements.shareWhatsapp.addEventListener('click', shareViaWhatsapp);
            }
            if (elements.shareEmail) {
                elements.shareEmail.addEventListener('click', shareViaEmail);
            }
            
            // Professional management
            const addProfessionalBtn = document.getElementById('addProfessional');
            if (addProfessionalBtn) {
                addProfessionalBtn.addEventListener('click', addProfessionalHandler);
            }
            
            // Box management
            const addBoxBtn = document.getElementById('addBox');
            if (addBoxBtn) {
                addBoxBtn.addEventListener('click', addBoxHandler);
            }
            
            // View controls
            document.querySelectorAll('.view-btn').forEach(btn => {
                btn.addEventListener('click', (e) => {
                    document.querySelectorAll('.view-btn').forEach(b => b.classList.remove('active'));
                    e.target.classList.add('active');
                    state.currentView = e.target.dataset.view;
                    renderCalendar();
                });
            });
            
            // Close dropdowns when clicking outside
            document.addEventListener('click', (e) => {
                if (elements.professionalDropdown && !elements.professionalDropdown.contains(e.target)) {
                    elements.professionalDropdown.classList.remove('active');
                }
            });
            
            // Close modals when clicking outside
            document.addEventListener('click', (e) => {
                if (e.target.classList.contains('modal-overlay')) {
                    e.target.classList.remove('active');
                }
            });
        }

        // Setup touch gestures
        function setupTouchGestures() {
            const calendar = document.querySelector('.calendar-container');
            
            calendar.addEventListener('touchstart', (e) => {
                state.touchStartX = e.changedTouches[0].screenX;
            });
            
            calendar.addEventListener('touchend', (e) => {
                state.touchEndX = e.changedTouches[0].screenX;
                handleSwipe();
            });
            
            function handleSwipe() {
                const swipeThreshold = 50;
                const diff = state.touchStartX - state.touchEndX;
                
                if (Math.abs(diff) > swipeThreshold) {
                    if (diff > 0) {
                        // Swipe left - next month
                        state.currentDate.setMonth(state.currentDate.getMonth() + 1);
                    } else {
                        // Swipe right - previous month
                        state.currentDate.setMonth(state.currentDate.getMonth() - 1);
                    }
                    renderCalendar();
                    showTouchIndicator();
                }
            }
        }

        // Show touch indicator
        function showTouchIndicator() {
            elements.touchIndicator.classList.add('show');
            setTimeout(() => {
                elements.touchIndicator.classList.remove('show');
            }, 2000);
        }

        // Render calendar
        function renderCalendar() {
            const year = state.currentDate.getFullYear();
            const month = state.currentDate.getMonth();
            
            // Update month/year display
            const months = ['Enero', 'Febrero', 'Marzo', 'Abril', 'Mayo', 'Junio', 
                               'Julio', 'Agosto', 'Septiembre', 'Octubre', 'Noviembre', 'Diciembre'];
            elements.currentMonth.textContent = `${months[month]} ${year}`;
            
            // Update box availability for today initially
            const today = new Date();
            const todayStr = `${today.getFullYear()}-${String(today.getMonth() + 1).padStart(2, '0')}-${String(today.getDate()).padStart(2, '0')}`;
            renderBoxAvailability(todayStr);
            
            // Clear calendar body
            elements.calendarBody.innerHTML = '';
            
            // Get first day of month
            const firstDay = new Date(year, month, 1);
            
            // Find first Monday (or first day if it's Monday)
            let currentDate = new Date(firstDay);
            while (currentDate.getDay() !== 1) { // 1 = Monday
                currentDate.setDate(currentDate.getDate() - 1);
            }
            
            // Generate all weekdays for the month and surrounding weeks to complete the grid
            const datesToShow = [];
            const lastDayOfMonth = new Date(year, month + 1, 0).getDate();
            const lastDateOfMonth = new Date(year, month, lastDayOfMonth);
            
            // Continue until we have shown all days of the current month and completed the week
            while (true) {
                if (currentDate.getDay() >= 1 && currentDate.getDay() <= 5) { // Monday to Friday
                    datesToShow.push(new Date(currentDate));
                }
                
                // Check if we've passed the last day of the current month
                // and we're at the end of a week (Friday) or we have enough days
                if (currentDate > lastDateOfMonth && 
                    (currentDate.getDay() === 5 || datesToShow.length >= 35)) {
                    break;
                }
                
                currentDate.setDate(currentDate.getDate() + 1);
                
                // Safety break to avoid infinite loops
                if (datesToShow.length >= 42) break; // 6 weeks * 7 days max
            }
            
            // Create day columns for each date
            datesToShow.forEach((date, index) => {
                const dayColumn = document.createElement('div');
                dayColumn.className = 'day-column';
                
                // Day header
                const dayDate = document.createElement('div');
                dayDate.className = 'day-date';
                
                const dateNum = date.getDate();
                const isCurrentMonth = date.getMonth() === month;
                const actualYear = date.getFullYear();
                const actualMonth = date.getMonth();
                
                dayDate.textContent = dateNum;
                
                if (!isCurrentMonth) {
                    dayDate.classList.add('other-month');
                }
                
                // Check if today
                const today = new Date();
                if (actualYear === today.getFullYear() && 
                    actualMonth === today.getMonth() && 
                    dateNum === today.getDate()) {
                    dayDate.classList.add('today');
                }
                
                dayColumn.appendChild(dayDate);
                
                // Appointments container
                const appointmentsContainer = document.createElement('div');
                appointmentsContainer.className = 'appointments-container';
                
                // Add appointments for this day - organized by box
                const dayDateStr = `${actualYear}-${String(actualMonth + 1).padStart(2, '0')}-${String(dateNum).padStart(2, '0')}`;
                const dayAppointments = state.appointments.filter(
                    apt => apt.date === dayDateStr
                );
                
                // Group appointments by box
                const appointmentsByBox = {};
                dayAppointments.forEach(appointment => {
                    const boxId = appointment.box.id;
                    if (!appointmentsByBox[boxId]) {
                        appointmentsByBox[boxId] = {
                            box: appointment.box,
                            AM: null,
                            PM: null
                        };
                    }
                    appointmentsByBox[boxId][appointment.shift] = appointment;
                });
                
                // Sort boxes by ID for consistent ordering
                const sortedBoxes = Object.keys(appointmentsByBox).sort((a, b) => parseInt(a) - parseInt(b));
                
                sortedBoxes.forEach(boxId => {
                    const boxData = appointmentsByBox[boxId];
                    const boxRowEl = document.createElement('div');
                    boxRowEl.className = 'box-appointment-row';
                    boxRowEl.style.cssText = `
                        display: flex;
                        gap: 4px;
                        margin-bottom: 8px;
                        width: 100%;
                    `;
                    
                    // AM slot (left side)
                    const amSlot = document.createElement('div');
                    amSlot.className = 'appointment-slot am-slot';
                    amSlot.style.cssText = `
                        flex: 1;
                        min-height: 60px;
                        border-radius: 6px;
                        padding: 6px;
                        font-size: 11px;
                        cursor: pointer;
                        transition: all 0.2s ease;
                        display: flex;
                        flex-direction: column;
                        justify-content: center;
                        text-align: center;
                        position: relative;
                    `;
                    
                    if (boxData.AM) {
                        const abbreviatedName = abbreviateProfessionalName(boxData.AM.professional.name);
                        amSlot.style.cssText += `
                            background: linear-gradient(135deg, #e0f2fe 0%, #f0f9ff 100%);
                            border: 1px solid #0ea5e9;
                            color: #0c4a6e;
                        `;
                        amSlot.innerHTML = `
                            <div style="font-weight: 600; font-size: 10px; margin-bottom: 2px;">${boxData.box.name}</div>
                            <div style="font-weight: 700; font-size: 12px; margin-bottom: 2px;">AM</div>
                            <div style="font-size: 10px; font-weight: 500;">${abbreviatedName}</div>
                            <div class="appointment-actions" style="position: absolute; top: 2px; right: 2px; display: none;">
                                <button class="appointment-action edit" onclick="editAppointmentFromCalendar(${boxData.AM.id})" style="font-size: 10px; padding: 2px;">✏️</button>
                                <button class="appointment-action delete" onclick="deleteAppointmentFromCalendar(${boxData.AM.id})" style="font-size: 10px; padding: 2px;">🗑️</button>
                            </div>
                        `;
                        amSlot.addEventListener('click', (e) => {
                            if (!e.target.classList.contains('appointment-action')) {
                                showAppointmentDetails(boxData.AM);
                            }
                        });
                        amSlot.addEventListener('mouseenter', () => {
                            amSlot.querySelector('.appointment-actions').style.display = 'block';
                        });
                        amSlot.addEventListener('mouseleave', () => {
                            amSlot.querySelector('.appointment-actions').style.display = 'none';
                        });
                    } else {
                        amSlot.style.cssText += `
                            background: linear-gradient(135deg, #fce7f3 0%, #fdf2f8 100%);
                            border: 1px dashed #f9a8d4;
                            color: #be185d;
                            opacity: 0.6;
                        `;
                        amSlot.innerHTML = `
                            <div style="font-weight: 600; font-size: 10px; margin-bottom: 2px;">${boxData.box.name}</div>
                            <div style="font-weight: 700; font-size: 12px; margin-bottom: 2px;">AM</div>
                            <div style="font-size: 9px; font-style: italic;">Disponible</div>
                        `;
                    }
                    
                    // PM slot (right side)
                    const pmSlot = document.createElement('div');
                    pmSlot.className = 'appointment-slot pm-slot';
                    pmSlot.style.cssText = `
                        flex: 1;
                        min-height: 60px;
                        border-radius: 6px;
                        padding: 6px;
                        font-size: 11px;
                        cursor: pointer;
                        transition: all 0.2s ease;
                        display: flex;
                        flex-direction: column;
                        justify-content: center;
                        text-align: center;
                        position: relative;
                    `;
                    
                    if (boxData.PM) {
                        const abbreviatedName = abbreviateProfessionalName(boxData.PM.professional.name);
                        pmSlot.style.cssText += `
                            background: linear-gradient(135deg, #fef3c7 0%, #fffbeb 100%);
                            border: 1px solid #f59e0b;
                            color: #92400e;
                        `;
                        pmSlot.innerHTML = `
                            <div style="font-weight: 600; font-size: 10px; margin-bottom: 2px;">${boxData.box.name}</div>
                            <div style="font-weight: 700; font-size: 12px; margin-bottom: 2px;">PM</div>
                            <div style="font-size: 10px; font-weight: 500;">${abbreviatedName}</div>
                            <div class="appointment-actions" style="position: absolute; top: 2px; right: 2px; display: none;">
                                <button class="appointment-action edit" onclick="editAppointmentFromCalendar(${boxData.PM.id})" style="font-size: 10px; padding: 2px;">✏️</button>
                                <button class="appointment-action delete" onclick="deleteAppointmentFromCalendar(${boxData.PM.id})" style="font-size: 10px; padding: 2px;">🗑️</button>
                            </div>
                        `;
                        pmSlot.addEventListener('click', (e) => {
                            if (!e.target.classList.contains('appointment-action')) {
                                showAppointmentDetails(boxData.PM);
                            }
                        });
                        pmSlot.addEventListener('mouseenter', () => {
                            pmSlot.querySelector('.appointment-actions').style.display = 'block';
                        });
                        pmSlot.addEventListener('mouseleave', () => {
                            pmSlot.querySelector('.appointment-actions').style.display = 'none';
                        });
                    } else {
                        pmSlot.style.cssText += `
                            background: linear-gradient(135deg, #f3f4f6 0%, #f9fafb 100%);
                            border: 1px dashed #d1d5db;
                            color: #6b7280;
                            opacity: 0.5;
                        `;
                        pmSlot.innerHTML = `
                            <div style="font-weight: 600; font-size: 10px; margin-bottom: 2px;">${boxData.box.name}</div>
                            <div style="font-weight: 700; font-size: 12px; margin-bottom: 2px;">PM</div>
                            <div style="font-size: 9px; font-style: italic;">Disponible</div>
                        `;
                    }
                    
                    boxRowEl.appendChild(amSlot);
                    boxRowEl.appendChild(pmSlot);
                    appointmentsContainer.appendChild(boxRowEl);
                });
                
                // Add button to create new appointment
                const addAppointmentBtn = document.createElement('button');
                addAppointmentBtn.className = 'add-appointment-btn';
                addAppointmentBtn.textContent = '+ Agregar turno';
                
                if (isCurrentMonth) {
                    addAppointmentBtn.addEventListener('click', () => {
                        openAppointmentModal(actualYear, actualMonth, dateNum);
                    });
                } else {
                    addAppointmentBtn.style.visibility = 'hidden';
                }
                
                appointmentsContainer.appendChild(addAppointmentBtn);
                dayColumn.appendChild(appointmentsContainer);
                
                // Add share button if there are appointments for this day
                if (dayAppointments.length > 0) {
                    const shareButton = document.createElement('button');
                    shareButton.className = 'day-share-button';
                    shareButton.innerHTML = '📤';
                    shareButton.title = 'Compartir agenda del día';
                    
                    shareButton.addEventListener('click', (e) => {
                        e.stopPropagation();
                        openShareModal(dayDateStr);
                    });
                    
                    dayColumn.appendChild(shareButton);
                }
                
                // Add click listener to update box availability for this day
                dayColumn.addEventListener('click', (e) => {
                    // Only update if clicking on the day itself, not on appointments or buttons
                    if (e.target === dayColumn || e.target === dayDate || e.target === appointmentsContainer) {
                        const dayDateStr = `${actualYear}-${String(actualMonth + 1).padStart(2, '0')}-${String(dateNum).padStart(2, '0')}`;
                        renderBoxAvailability(dayDateStr);
                    }
                });
                
                elements.calendarBody.appendChild(dayColumn);
            });
        }

        // Helper function to abbreviate professional names
        function abbreviateProfessionalName(fullName) {
            if (!fullName) return '';
            
            const nameParts = fullName.trim().split(' ');
            if (nameParts.length === 1) {
                // Solo un nombre, devolver las primeras 2 letras
                return nameParts[0].substring(0, 2).toUpperCase();
            } else if (nameParts.length >= 2) {
                // Nombre y apellido, devolver primera inicial + apellido
                const firstInitial = nameParts[0].charAt(0).toUpperCase();
                const lastName = nameParts[nameParts.length - 1];
                return `${firstInitial}. ${lastName}`;
            }
            return fullName;
        }

        // Calculate professional KPIs for current month
        function calculateProfessionalKPIs() {
            const currentDate = new Date();
            const currentYear = currentDate.getFullYear();
            const currentMonth = currentDate.getMonth();
            
            // Get first and last day of current month
            const firstDay = new Date(currentYear, currentMonth, 1);
            const lastDay = new Date(currentYear, currentMonth + 1, 0);
            const totalDaysInMonth = lastDay.getDate();
            
            // Filter appointments for current month
            const monthAppointments = state.appointments.filter(apt => {
                const aptDate = new Date(apt.date);
                return aptDate.getFullYear() === currentYear && aptDate.getMonth() === currentMonth;
            });
            
            const professionalStats = {};
            
            // Initialize stats for each professional
            state.professionals.forEach(prof => {
                professionalStats[prof.id] = {
                    name: prof.name,
                    abbreviatedName: abbreviateProfessionalName(prof.name),
                    amCount: 0,
                    pmCount: 0,
                    totalCount: 0,
                    daysWorked: new Set(),
                    percentage: 0,
                    attendancePercentage: 0
                };
            });
            
            // Count appointments by professional and shift
            monthAppointments.forEach(apt => {
                if (apt.professional && professionalStats[apt.professional.id]) {
                    const stats = professionalStats[apt.professional.id];
                    
                    if (apt.shift === 'AM') {
                        stats.amCount++;
                    } else if (apt.shift === 'PM') {
                        stats.pmCount++;
                    }
                    
                    stats.totalCount++;
                    
                    // Track unique days worked
                    const aptDate = new Date(apt.date);
                    const dayKey = aptDate.getDate();
                    stats.daysWorked.add(dayKey);
                }
            });
            
            // Calculate percentages
            const totalAppointments = monthAppointments.length;
            
            Object.values(professionalStats).forEach(stats => {
                // Percentage of total appointments
                stats.percentage = totalAppointments > 0 ? 
                    Math.round((stats.totalCount / totalAppointments) * 100) : 0;
                
                // Attendance percentage (days worked vs total days in month)
                stats.attendancePercentage = Math.round((stats.daysWorked.size / totalDaysInMonth) * 100);
            });
            
            return professionalStats;
        }

        // Function to populate professionals dropdown in appointment modal (filtered by "Vigente" status)
        function populateProfessionals() {
            const professionalMenu = document.getElementById('professionalMenu');
            if (!professionalMenu) return;
            
            // Clear existing options
            professionalMenu.innerHTML = '';
            
            // Filter professionals to show only those with "Vigente" status
            const activeProfessionals = state.professionals.filter(prof => prof.status === 'Vigente');
            
            if (activeProfessionals.length === 0) {
                professionalMenu.innerHTML = '<div class="dropdown-item disabled">No hay profesionales vigentes disponibles</div>';
                return;
            }
            
            // Add each active professional to the dropdown
            activeProfessionals.forEach(professional => {
                const professionalItem = document.createElement('div');
                professionalItem.className = 'dropdown-item';
                professionalItem.dataset.professionalId = professional.id;
                
                professionalItem.innerHTML = `
                    <div class="professional-avatar">${professional.avatar}</div>
                    <div class="professional-info">
                        <div class="professional-name">${professional.name}</div>
                    </div>
                `;
                
                // Add click event to select professional
                professionalItem.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    
                    // Update selected professional
                    state.selectedProfessional = professional;
                    
                    // Update dropdown display
                    const toggle = document.querySelector('#professionalDropdown .dropdown-toggle');
                    if (toggle) {
                        toggle.innerHTML = `${professional.name} <span>▼</span>`;
                    }
                    
                    // Close dropdown
                    document.getElementById('professionalDropdown').classList.remove('active');
                });
                
                professionalMenu.appendChild(professionalItem);
            });
        }

        // Function to populate professionals dropdown in details modal (filtered by "Vigente" status)
        function populateDetailsProfessionals() {
            const detailsProfessionalMenu = document.getElementById('detailsProfessionalMenu');
            if (!detailsProfessionalMenu) return;
            
            // Clear existing options
            detailsProfessionalMenu.innerHTML = '';
            
            // Filter professionals to show only those with "Vigente" status
            const activeProfessionals = state.professionals.filter(prof => prof.status === 'Vigente');
            
            if (activeProfessionals.length === 0) {
                detailsProfessionalMenu.innerHTML = '<div class="dropdown-item disabled">No hay profesionales vigentes disponibles</div>';
                return;
            }
            
            // Add each active professional to the dropdown
            activeProfessionals.forEach(professional => {
                const professionalItem = document.createElement('div');
                professionalItem.className = 'dropdown-item';
                professionalItem.dataset.professionalId = professional.id;
                
                professionalItem.innerHTML = `
                    <div class="professional-avatar">${professional.avatar}</div>
                    <div class="professional-info">
                        <div class="professional-name">${professional.name}</div>
                    </div>
                `;
                
                detailsProfessionalMenu.appendChild(professionalItem);
            });
        }

        // Modal functions
        function openModal(modalId) {
            document.getElementById(modalId).classList.add('active');
        }

        function closeModal(modalId) {
            document.getElementById(modalId).classList.remove('active');
        }

        // Open appointment modal
        function openAppointmentModal(year, month, day) {
            const dateStr = `${String(day).padStart(2, '0')}/${String(month + 1).padStart(2, '0')}/${year}`;
            elements.appointmentDate.value = dateStr;
            
            // Reset form
            elements.appointmentShift.value = '';
            state.selectedProfessional = null;
            state.selectedBox = null;
            
            // Populate professionals and boxes with current data
            populateProfessionals();
            populateBoxes();
            
            // Update professional dropdown display
            const toggle = elements.professionalDropdown.querySelector('.dropdown-toggle');
            toggle.innerHTML = 'Seleccionar profesional <span>▼</span>';
            
            // Reset box selection
            document.querySelectorAll('.box-card').forEach(card => {
                card.classList.remove('selected');
            });
            
            openModal('appointmentModal');
        }

        // Show appointment details
        function showAppointmentDetails(appointment) {
            state.selectedAppointment = appointment;
            
            // Convert date format from YYYY-MM-DD to DD/MM/YYYY for display
            const dateParts = appointment.date.split('-');
            const dateStr = `${dateParts[2]}/${dateParts[1]}/${dateParts[0]}`;
            
            // Fill the form fields
            document.getElementById('detailsDate').value = dateStr;
            document.getElementById('detailsShift').value = appointment.shift;
            
            // Update professional dropdown display
            const professionalToggle = document.querySelector('#detailsProfessionalDropdown .dropdown-toggle');
            if (professionalToggle) {
                professionalToggle.innerHTML = `${appointment.professional.name} <span>▼</span>`;
            }
            
            // Update box selection - clear all first, then select the correct one
            document.querySelectorAll('#detailsBoxesContainer .box-card').forEach(card => {
                card.classList.remove('selected');
            });
            
            // Populate boxes in details modal if not already populated
            populateDetailsBoxes(appointment.box);
            
            openModal('appointmentDetailsModal');
        }

        // Populate boxes for details modal
        function populateDetailsBoxes(selectedBox) {
            const detailsBoxesContainer = document.getElementById('detailsBoxesContainer');
            if (!detailsBoxesContainer) return;
            
            detailsBoxesContainer.innerHTML = '';
            
            state.boxes.forEach(box => {
                const boxCard = document.createElement('div');
                boxCard.className = 'box-card';
                boxCard.dataset.boxId = box.id;
                
                // Mark as selected if this is the appointment's box
                if (selectedBox && box.id === selectedBox.id) {
                    boxCard.classList.add('selected');
                }
                
                boxCard.innerHTML = `
                    <div class="box-name">${box.name}</div>
                    <div class="box-info">${box.location} • ${box.equipment}</div>
                `;
                
                // Disable click events since this is read-only
                boxCard.style.pointerEvents = 'none';
                boxCard.style.opacity = '0.8';
                
                detailsBoxesContainer.appendChild(boxCard);
            });
        }

        // Check for professional conflict
        function checkProfessionalConflict(professionalId, date, shift, excludeAppointmentId = null) {
            return state.appointments.find(apt => 
                apt.professional.id === professionalId &&
                apt.date === date &&
                apt.shift === shift &&
                apt.id !== excludeAppointmentId
            );
        }
        
        // Check for box conflict
        function checkBoxConflict(boxId, date, shift, excludeAppointmentId = null) {
            return state.appointments.find(apt => 
                apt.box.id === boxId &&
                apt.date === date &&
                apt.shift === shift &&
                apt.id !== excludeAppointmentId
            );
        }

        // Show professional conflict popup
        function showProfessionalConflictPopup(conflictAppointment) {
            console.log('showProfessionalConflictPopup called with:', conflictAppointment); // Debug log
            
            const dateParts = conflictAppointment.date.split('-');
            const dateStr = `${dateParts[2]}/${dateParts[1]}/${dateParts[0]}`;
            
            // Remove any existing conflict modals first
            const existingConflictModals = document.querySelectorAll('.conflict-modal-overlay');
            existingConflictModals.forEach(modal => modal.remove());
            
            // Create and show custom modal with very high z-index
            const conflictModal = document.createElement('div');
            conflictModal.className = 'conflict-modal-overlay';
            conflictModal.id = 'conflictModal';
            
            // Use inline styles with very high z-index
            conflictModal.style.cssText = `
                position: fixed !important;
                top: 0 !important;
                left: 0 !important;
                width: 100vw !important;
                height: 100vh !important;
                background-color: rgba(0, 0, 0, 0.8) !important;
                display: flex !important;
                justify-content: center !important;
                align-items: center !important;
                z-index: 999999 !important;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
            `;
            
            conflictModal.innerHTML = `
                <div style="
                    background: white !important;
                    border-radius: 12px !important;
                    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25) !important;
                    max-width: 500px !important;
                    width: 90% !important;
                    max-height: 90vh !important;
                    overflow-y: auto !important;
                    position: relative !important;
                    z-index: 1000000 !important;
                ">
                    <div style="
                        padding: 24px 24px 0 24px !important;
                        border-bottom: 1px solid #e5e7eb !important;
                        display: flex !important;
                        justify-content: space-between !important;
                        align-items: center !important;
                    ">
                        <h2 style="
                            margin: 0 !important;
                            font-size: 20px !important;
                            font-weight: 600 !important;
                            color: #dc2626 !important;
                        ">⚠️ Conflicto de Agendamiento</h2>
                        <button onclick="document.getElementById('conflictModal').remove()" style="
                            background: none !important;
                            border: none !important;
                            font-size: 24px !important;
                            cursor: pointer !important;
                            color: #6b7280 !important;
                            padding: 0 !important;
                            width: 32px !important;
                            height: 32px !important;
                            display: flex !important;
                            align-items: center !important;
                            justify-content: center !important;
                        ">&times;</button>
                    </div>
                    <div style="padding: 24px !important;">
                        <div style="text-align: center !important;">
                            <div style="font-size: 48px !important; margin-bottom: 16px !important;">⚠️</div>
                            <h3 style="color: #dc2626 !important; margin-bottom: 16px !important; font-size: 18px !important;">Conflicto de Agendamiento</h3>
                            <p style="margin-bottom: 20px !important; color: #374151 !important; font-size: 16px !important;">
                                El profesional <strong>${conflictAppointment.professional.name}</strong> ya tiene un turno agendado:
                            </p>
                            <div style="
                                background: #f3f4f6 !important;
                                padding: 16px !important;
                                border-radius: 8px !important;
                                margin-bottom: 20px !important;
                                text-align: left !important;
                            ">
                                <div style="margin-bottom: 8px !important; font-size: 14px !important;"><strong>📅 Día:</strong> ${dateStr}</div>
                                <div style="margin-bottom: 8px !important; font-size: 14px !important;"><strong>🕐 Turno:</strong> ${conflictAppointment.shift}</div>
                                <div style="font-size: 14px !important;"><strong>🏥 Box:</strong> ${conflictAppointment.box.name}</div>
                            </div>
                            <p style="color: #6b7280 !important; font-size: 14px !important; margin-bottom: 0 !important;">
                                No es posible agendar al mismo profesional dos veces en el mismo día y turno.
                            </p>
                        </div>
                    </div>
                    <div style="
                        padding: 16px 24px 24px 24px !important;
                        border-top: 1px solid #e5e7eb !important;
                        display: flex !important;
                        justify-content: center !important;
                    ">
                        <button onclick="document.getElementById('conflictModal').remove()" style="
                            background: #3b82f6 !important;
                            color: white !important;
                            border: none !important;
                            padding: 12px 24px !important;
                            border-radius: 8px !important;
                            font-size: 14px !important;
                            font-weight: 500 !important;
                            cursor: pointer !important;
                            transition: background-color 0.2s !important;
                        " onmouseover="this.style.backgroundColor='#2563eb'" onmouseout="this.style.backgroundColor='#3b82f6'">
                            ✅ Entendido
                        </button>
                    </div>
                </div>
            `;
            
            document.body.appendChild(conflictModal);
            console.log('Conflict modal added to DOM'); // Debug log
            
            // Auto-remove after clicking outside
            conflictModal.addEventListener('click', (e) => {
                if (e.target === conflictModal) {
                    conflictModal.remove();
                }
            });
        }
        
        // Show box conflict popup
        function showBoxConflictPopup(conflictAppointment) {
            console.log('showBoxConflictPopup called with:', conflictAppointment); // Debug log
            
            const dateParts = conflictAppointment.date.split('-');
            const dateStr = `${dateParts[2]}/${dateParts[1]}/${dateParts[0]}`;
            
            // Remove any existing conflict modals first
            const existingConflictModals = document.querySelectorAll('.conflict-modal-overlay');
            existingConflictModals.forEach(modal => modal.remove());
            
            // Create and show custom modal with very high z-index
            const conflictModal = document.createElement('div');
            conflictModal.className = 'conflict-modal-overlay';
            conflictModal.id = 'boxConflictModal';
            
            // Use inline styles with very high z-index
            conflictModal.style.cssText = `
                position: fixed !important;
                top: 0 !important;
                left: 0 !important;
                width: 100vw !important;
                height: 100vh !important;
                background-color: rgba(0, 0, 0, 0.8) !important;
                display: flex !important;
                justify-content: center !important;
                align-items: center !important;
                z-index: 999999 !important;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
            `;
            
            conflictModal.innerHTML = `
                <div style="
                    background: white !important;
                    border-radius: 12px !important;
                    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25) !important;
                    max-width: 500px !important;
                    width: 90% !important;
                    max-height: 90vh !important;
                    overflow-y: auto !important;
                    position: relative !important;
                    z-index: 1000000 !important;
                ">
                    <div style="
                        padding: 24px 24px 0 24px !important;
                        border-bottom: 1px solid #e5e7eb !important;
                        display: flex !important;
                        justify-content: space-between !important;
                        align-items: center !important;
                    ">
                        <h2 style="
                            margin: 0 !important;
                            font-size: 20px !important;
                            font-weight: 600 !important;
                            color: #dc2626 !important;
                        ">🏥 Conflicto de Box</h2>
                        <button onclick="document.getElementById('boxConflictModal').remove()" style="
                            background: none !important;
                            border: none !important;
                            font-size: 24px !important;
                            cursor: pointer !important;
                            color: #6b7280 !important;
                            padding: 0 !important;
                            width: 32px !important;
                            height: 32px !important;
                            display: flex !important;
                            align-items: center !important;
                            justify-content: center !important;
                        ">&times;</button>
                    </div>
                    <div style="padding: 24px !important;">
                        <div style="text-align: center !important;">
                            <div style="font-size: 48px !important; margin-bottom: 16px !important;">🏥</div>
                            <h3 style="color: #dc2626 !important; margin-bottom: 16px !important; font-size: 18px !important;">Box No Disponible</h3>
                            <p style="margin-bottom: 20px !important; color: #374151 !important; font-size: 16px !important;">
                                El <strong>${conflictAppointment.box.name}</strong> ya está ocupado en este horario:
                            </p>
                            <div style="
                                background: #f3f4f6 !important;
                                padding: 16px !important;
                                border-radius: 8px !important;
                                margin-bottom: 20px !important;
                                text-align: left !important;
                            ">
                                <div style="margin-bottom: 8px !important; font-size: 14px !important;"><strong>📅 Día:</strong> ${dateStr}</div>
                                <div style="margin-bottom: 8px !important; font-size: 14px !important;"><strong>🕐 Turno:</strong> ${conflictAppointment.shift}</div>
                                <div style="margin-bottom: 8px !important; font-size: 14px !important;"><strong>🏥 Box:</strong> ${conflictAppointment.box.name}</div>
                                <div style="font-size: 14px !important;"><strong>👨‍⚕️ Profesional:</strong> ${conflictAppointment.professional.name}</div>
                            </div>
                            <p style="color: #6b7280 !important; font-size: 14px !important; margin-bottom: 0 !important;">
                                Un box no puede tener más de un agendamiento en el mismo día y turno.
                            </p>
                        </div>
                    </div>
                    <div style="
                        padding: 16px 24px 24px 24px !important;
                        border-top: 1px solid #e5e7eb !important;
                        display: flex !important;
                        justify-content: center !important;
                    ">
                        <button onclick="document.getElementById('boxConflictModal').remove()" style="
                            background: #3b82f6 !important;
                            color: white !important;
                            border: none !important;
                            padding: 12px 24px !important;
                            border-radius: 8px !important;
                            font-size: 14px !important;
                            font-weight: 500 !important;
                            cursor: pointer !important;
                            transition: background-color 0.2s !important;
                        " onmouseover="this.style.backgroundColor='#2563eb'" onmouseout="this.style.backgroundColor='#3b82f6'">
                            ✅ Entendido
                        </button>
                    </div>
                </div>
            `;
            
            document.body.appendChild(conflictModal);
            console.log('Box conflict modal added to DOM'); // Debug log
            
            // Auto-remove after clicking outside
            conflictModal.addEventListener('click', (e) => {
                if (e.target === conflictModal) {
                    conflictModal.remove();
                }
            });
        }

        // Save appointment
        function saveAppointmentHandler() {
            console.log('saveAppointmentHandler called'); // Debug log
            console.log('appointmentDate:', elements.appointmentDate.value); // Debug log
            console.log('appointmentShift:', elements.appointmentShift.value); // Debug log
            console.log('selectedProfessional:', state.selectedProfessional); // Debug log
            console.log('selectedBox:', state.selectedBox); // Debug log
            console.log('editingAppointment:', state.editingAppointment); // Debug log
            
            const dateParts = elements.appointmentDate.value.split('/');
            const dateStr = `${dateParts[2]}-${dateParts[1]}-${dateParts[0]}`;
            
            if (!elements.appointmentShift.value || 
                !state.selectedProfessional ||
                !state.selectedBox) {
                alert('Por favor complete todos los campos obligatorios');
                return;
            }
            
            // Check for professional conflict (exclude current appointment if editing)
            const excludeId = state.editingAppointment ? state.editingAppointment.id : null;
            const professionalConflict = checkProfessionalConflict(
                state.selectedProfessional.id, 
                dateStr, 
                elements.appointmentShift.value,
                excludeId
            );
            
            if (professionalConflict) {
                showProfessionalConflictPopup(professionalConflict);
                return;
            }
            
            // Check for box conflict (exclude current appointment if editing)
            const boxConflict = checkBoxConflict(
                state.selectedBox.id,
                dateStr,
                elements.appointmentShift.value,
                excludeId
            );
            
            if (boxConflict) {
                showBoxConflictPopup(boxConflict);
                return;
            }
            
            if (state.editingAppointment) {
                // Update existing appointment
                const appointmentIndex = state.appointments.findIndex(apt => apt.id === state.editingAppointment.id);
                if (appointmentIndex !== -1) {
                    state.appointments[appointmentIndex] = {
                        ...state.appointments[appointmentIndex],
                        date: dateStr,
                        shift: elements.appointmentShift.value,
                        professional: state.selectedProfessional,
                        box: state.selectedBox
                    };
                    console.log('Appointment updated successfully'); // Debug log
                    alert('Turno actualizado correctamente');
                } else {
                    alert('Error: No se pudo encontrar el turno para actualizar');
                    return;
                }
                state.editingAppointment = null; // Clear editing mode
            } else {
                // Create new appointment
                const newAppointment = {
                    id: Date.now(),
                    date: dateStr,
                    shift: elements.appointmentShift.value,
                    professional: state.selectedProfessional,
                    box: state.selectedBox,
                    status: 'pending'
                };
                
                console.log('New appointment created:', newAppointment); // Debug log
                state.appointments.push(newAppointment);
                console.log('Appointment saved successfully'); // Debug log
            }
            
            renderCalendar();
            updateStats();
            closeModal('appointmentModal');
        }

        // Delete appointment
        function deleteAppointmentHandler() {
            if (state.selectedAppointment && confirm('¿Está seguro de que desea eliminar esta cita?')) {
                state.appointments = state.appointments.filter(
                    apt => apt.id !== state.selectedAppointment.id
                );
                renderCalendar();
                updateStats();
                closeModal('appointmentDetailsModal');
            }
        }

        // Edit appointment
        function editAppointmentHandler() {
            if (state.selectedAppointment) {
                const apt = state.selectedAppointment;
                const dateParts = apt.date.split('-');
                const dateStr = `${dateParts[2]}/${dateParts[1]}/${dateParts[0]}`;
                
                elements.appointmentDate.value = dateStr;
                elements.appointmentShift.value = apt.shift;
                
                state.selectedProfessional = apt.professional;
                state.selectedBox = apt.box;
                state.editingAppointment = apt; // Set editing mode
                
                // Update professional dropdown display
                const toggle = elements.professionalDropdown.querySelector('.dropdown-toggle');
                toggle.innerHTML = `${apt.professional.name} <span>▼</span>`;
                
                // Update box selection
                document.querySelectorAll('.box-card').forEach(card => {
                    card.classList.remove('selected');
                    if (card.dataset.boxId == apt.box.id) {
                        card.classList.add('selected');
                    }
                });
                
                closeModal('appointmentDetailsModal');
                openModal('appointmentModal');
            }
        }

        // Edit appointment from calendar
        function editAppointmentFromCalendar(appointmentId) {
            const appointment = state.appointments.find(apt => apt.id === appointmentId);
            if (appointment) {
                state.selectedAppointment = appointment;
                editAppointmentHandler();
            }
        }

        // Delete appointment from calendar
        function deleteAppointmentFromCalendar(appointmentId) {
            const appointment = state.appointments.find(apt => apt.id === appointmentId);
            if (appointment && confirm('¿Está seguro de que desea eliminar esta cita?')) {
                state.appointments = state.appointments.filter(apt => apt.id !== appointmentId);
                renderCalendar();
                updateStats();
            }
        }

        // Populate professionals dropdown
        function populateProfessionals() {
            elements.professionalMenu.innerHTML = '';
            
            state.professionals.forEach(professional => {
                const item = document.createElement('div');
                item.className = 'dropdown-item';
                item.innerHTML = `
                    <div style="display: flex; align-items: center; gap: 10px;">
                        <div class="professional-avatar">${professional.avatar}</div>
                        <div>
                            <div style="font-weight: 600;">${professional.name}</div>
                        </div>
                    </div>
                `;
                
                item.addEventListener('click', (e) => {
                    e.stopPropagation(); // Evita que el click llegue al toggle del dropdown
                    state.selectedProfessional = professional;
                    const toggle = elements.professionalDropdown.querySelector('.dropdown-toggle');
                    toggle.innerHTML = `${professional.name} <span>▼</span>`;
                    elements.professionalDropdown.classList.remove('active');
                });
                elements.professionalMenu.appendChild(item);
            });
        }

        // Populate professional list in sidebar
        function populateProfessionalList() {
            const professionalListElement = document.getElementById('professionalList');
            if (!professionalListElement) {
                console.error('Element professionalList not found');
                return;
            }
            
            professionalListElement.innerHTML = '';
            
            state.professionals.forEach(professional => {
                const item = document.createElement('div');
                item.className = 'professional-item';
                item.innerHTML = `
                    <div class="professional-avatar">${professional.avatar}</div>
                    <div class="professional-info">
                        <div class="professional-name">${professional.name}</div>
                        <div class="professional-specialty">${professional.specialty}</div>
                    </div>
                    <div class="professional-status"></div>
                `;
                
                item.addEventListener('click', () => {
                    document.querySelectorAll('.professional-item').forEach(el => el.classList.remove('active'));
                    item.classList.add('active');
                    state.selectedProfessional = professional;
                });
                
                professionalListElement.appendChild(item);
            });
        }

        // Populate boxes
        function populateBoxes() {
            elements.boxesContainer.innerHTML = '';
            
            state.boxes.forEach(box => {
                const boxCard = document.createElement('div');
                boxCard.className = 'box-card';
                boxCard.dataset.boxId = box.id;
                boxCard.innerHTML = `
                    <div class="box-name">${box.name}</div>
                    <div class="box-info">${box.location} • ${box.equipment}</div>
                `;
                
                boxCard.addEventListener('click', (e) => {
                    if (!e.target.classList.contains('action-btn')) {
                        // Remove selected class from all boxes
                        document.querySelectorAll('.box-card').forEach(card => {
                            card.classList.remove('selected');
                        });
                        
                        // Add selected class to clicked box
                        boxCard.classList.add('selected');
                        state.selectedBox = box;
                    }
                });
                
                elements.boxesContainer.appendChild(boxCard);
            });
        }

        // Populate professional management
        function populateProfessionalManagement() {
            elements.professionalListManagement.innerHTML = '';
            
            state.professionals.forEach(professional => {
                const item = document.createElement('div');
                item.className = 'professional-item-management';
                item.innerHTML = `
                    <div class="professional-info-management">
                        <div class="professional-avatar">${professional.avatar}</div>
                        <div class="professional-info">
                            <div class="professional-name">${professional.name}</div>
                            <div class="professional-specialty">${professional.specialty}</div>
                            <div style="font-size: 11px; color: #94a3b8;">${professional.email} • ${professional.phone}</div>
                        </div>
                    </div>
                    <div class="professional-actions">
                        <button class="action-btn edit" onclick="editProfessional(${professional.id})">✏️</button>
                        <button class="action-btn delete" onclick="deleteProfessional(${professional.id})">🗑️</button>
                    </div>
                `;
                
                elements.professionalListManagement.appendChild(item);
            });
        }

        // Populate box management
        function populateBoxManagement() {
            elements.boxListManagement.innerHTML = '';
            
            state.boxes.forEach(box => {
                const item = document.createElement('div');
                item.className = 'box-item-management';
                item.innerHTML = `
                    <div class="box-info-management">
                        <div class="box-name-management">${box.name}</div>
                        <div class="box-details-management">${box.location} • ${box.equipment}</div>
                        <div style="font-size: 11px; color: #94a3b8;">Estado: ${box.status === 'available' ? 'Disponible' : 'Ocupado'}</div>
                    </div>
                    <div class="professional-actions">
                        <button class="action-btn edit" onclick="editBox(${box.id})">✏️</button>
                        <button class="action-btn delete" onclick="deleteBox(${box.id})">🗑️</button>
                    </div>
                `;
                
                elements.boxListManagement.appendChild(item);
            });
        }

        // Professional management functions
        function addProfessionalHandler() {
            const name = document.getElementById('professionalName').value;
            const specialty = document.getElementById('professionalSpecialty').value;
            const email = document.getElementById('professionalEmail').value;
            const phone = document.getElementById('professionalPhone').value;
            
            if (!name || !specialty) {
                alert('Por favor complete el nombre y especialidad');
                return;
            }
            
            const newProfessional = {
                id: Date.now(),
                name: name,
                specialty: specialty,
                email: email,
                phone: phone,
                avatar: name.split(' ').map(n => n[0]).join('').toUpperCase(),
                status: 'active'
            };
            
            state.professionals.push(newProfessional);
            
            // Clear form
            document.getElementById('professionalName').value = '';
            document.getElementById('professionalSpecialty').value = '';
            document.getElementById('professionalEmail').value = '';
            document.getElementById('professionalPhone').value = '';
            
            // Update all professional lists
            populateProfessionals();
            populateProfessionalList();
            populateProfessionalManagement();
        }

        function editProfessional(id) {
            const professional = state.professionals.find(p => p.id === id);
            if (professional) {
                document.getElementById('professionalName').value = professional.name;
                document.getElementById('professionalSpecialty').value = professional.specialty;
                document.getElementById('professionalEmail').value = professional.email;
                document.getElementById('professionalPhone').value = professional.phone;
                
                // Remove the professional temporarily
                state.professionals = state.professionals.filter(p => p.id !== id);
                populateProfessionalManagement();
            }
        }

        function deleteProfessional(id) {
            if (confirm('¿Está seguro de que desea eliminar este profesional?')) {
                state.professionals = state.professionals.filter(p => p.id !== id);
                populateProfessionals();
                populateProfessionalList();
                populateProfessionalManagement();
            }
        }

        // Edit box from appointment modal
        function editBoxFromAppointmentModal(boxId) {
            const box = state.boxes.find(b => b.id === boxId);
            if (box) {
                // Fill the edit form with current box data
                elements.editBoxName.value = box.name;
                elements.editBoxLocation.value = box.location;
                elements.editBoxEquipment.value = box.equipment;
                elements.editBoxStatus.value = box.status;
                
                // Store the box ID for saving
                elements.editBoxModal.dataset.boxId = boxId;
                
                openModal('boxEditModal');
            }
        }

        // Save box edit
        function saveBoxEditHandler() {
            const boxId = parseInt(elements.editBoxModal.dataset.boxId);
            const box = state.boxes.find(b => b.id === boxId);
            
            if (box && elements.editBoxName.value && elements.editBoxLocation.value) {
                // Get the current date before making changes
                const currentDate = new Date();
                const changeDate = `${currentDate.getFullYear()}-${String(currentDate.getMonth() + 1).padStart(2, '0')}-${String(currentDate.getDate()).padStart(2, '0')}`;
                
                // Update box data
                box.name = elements.editBoxName.value;
                box.location = elements.editBoxLocation.value;
                box.equipment = elements.editBoxEquipment.value;
                box.status = elements.editBoxStatus.value;
                
                // Update all box displays
                populateBoxes();
                populateBoxManagement();
                updateStats();
                
                // Re-render calendar to show changes from today forward
                renderCalendar();
                
                // Show success message
                alert('Box actualizado correctamente. Los cambios se reflejarán desde hoy en adelante.');
                
                closeModal('boxEditModal');
            } else {
                alert('Por favor complete el nombre y ubicación del box.');
            }
        }

        // Box management functions
        function addBoxHandler() {
            const name = document.getElementById('boxName').value;
            const location = document.getElementById('boxLocation').value;
            const equipment = document.getElementById('boxEquipment').value;
            
            if (!name || !location) {
                alert('Por favor complete el nombre y ubicación');
                return;
            }
            
            const newBox = {
                id: Date.now(),
                name: name,
                location: location,
                equipment: equipment,
                status: 'available'
            };
            
            state.boxes.push(newBox);
            
            // Clear form
            document.getElementById('boxName').value = '';
            document.getElementById('boxLocation').value = '';
            document.getElementById('boxEquipment').value = '';
            
            // Update all box lists
            populateBoxes();
            populateBoxManagement();
            updateStats();
        }

        function editBox(id) {
            const box = state.boxes.find(b => b.id === id);
            if (box) {
                document.getElementById('boxName').value = box.name;
                document.getElementById('boxLocation').value = box.location;
                document.getElementById('boxEquipment').value = box.equipment;
                
                // Remove the box temporarily
                state.boxes = state.boxes.filter(b => b.id !== id);
                populateBoxManagement();
            }
        }

        function deleteBox(id) {
            if (confirm('¿Está seguro de que desea eliminar este box?')) {
                state.boxes = state.boxes.filter(b => b.id !== id);
                populateBoxes();
                populateBoxManagement();
                updateStats();
            }
        }

        // Settings functions
        function saveSettingsHandler() {
            state.settings.defaultDuration = parseInt(document.getElementById('defaultDuration').value);
            state.settings.startTime = document.getElementById('startTime').value;
            state.settings.endTime = document.getElementById('endTime').value;
            
            // Get notification settings
            const checkboxes = document.querySelectorAll('#settingsModal input[type="checkbox"]');
            state.settings.notifications.newAppointments = checkboxes[0].checked;
            state.settings.notifications.reminders = checkboxes[1].checked;
            state.settings.notifications.cancellations = checkboxes[2].checked;
            
            closeModal('settingsModal');
        }

        // Share functions
        let selectedShareDate = null;

        function openShareModal(dateStr) {
            selectedShareDate = dateStr;
            const modal = document.getElementById('shareOptionsModal');
            modal.style.display = 'flex';
        }

        function closeShareModal() {
            const modal = document.getElementById('shareOptionsModal');
            modal.style.display = 'none';
            selectedShareDate = null;
        }

        function generateDayAgendaText(dateStr) {
            // Format date for display
            const dateParts = dateStr.split('-');
            const displayDate = `${dateParts[2]}/${dateParts[1]}/${dateParts[0]}`;
            
            // Get appointments for the selected date
            const dayAppointments = state.appointments.filter(apt => apt.date === dateStr);
            
            if (dayAppointments.length === 0) {
                return `📅 Agenda del día ${displayDate}\n\nNo hay turnos agendados para este día.`;
            }

            // Group appointments by shift
            const amAppointments = dayAppointments.filter(apt => apt.shift === 'AM');
            const pmAppointments = dayAppointments.filter(apt => apt.shift === 'PM');

            let text = `🏥 AGENDA MÉDICA - ${displayDate}\n\n`;

            if (amAppointments.length > 0) {
                text += `🌅 TURNO MAÑANA (AM)\n`;
                text += `${'='.repeat(25)}\n`;
                amAppointments.forEach(apt => {
                    text += `🏥 ${apt.box.name}\n`;
                    text += `👨‍⚕️ ${apt.professional.name}\n\n`;
                });
            }

            if (pmAppointments.length > 0) {
                text += `🌆 TURNO TARDE (PM)\n`;
                text += `${'='.repeat(25)}\n`;
                pmAppointments.forEach(apt => {
                    text += `🏥 ${apt.box.name}\n`;
                    text += `👨‍⚕️ ${apt.professional.name}\n\n`;
                });
            }

            text += `📊 RESUMEN DEL DÍA\n`;
            text += `${'='.repeat(25)}\n`;
            text += `📅 Fecha: ${displayDate}\n`;
            text += `🔢 Total turnos: ${dayAppointments.length}\n`;
            text += `🌅 Turnos AM: ${amAppointments.length}\n`;
            text += `🌆 Turnos PM: ${pmAppointments.length}\n`;

            return text;
        }

        function shareViaWhatsApp() {
            if (!selectedShareDate) return;
            
            const text = generateDayAgendaText(selectedShareDate);
            const encodedText = encodeURIComponent(text);
            window.open(`https://wa.me/?text=${encodedText}`, '_blank');
            closeShareModal();
        }

        function shareViaEmail() {
            if (!selectedShareDate) return;
            
            const dateParts = selectedShareDate.split('-');
            const displayDate = `${dateParts[2]}/${dateParts[1]}/${dateParts[0]}`;
            
            const subject = encodeURIComponent(`Agenda Médica - ${displayDate}`);
            const body = encodeURIComponent(generateDayAgendaText(selectedShareDate));
            
            window.open(`mailto:?subject=${subject}&body=${body}`, '_blank');
            closeShareModal();
        }

        // Legacy share functions for individual appointments (keeping for compatibility)
        function shareViaWhatsapp() {
            if (!state.selectedAppointment) return;
            
            const apt = state.selectedAppointment;
            const text = `🏥 Turno médico agendado:\n\n` +
                         `📅 Día: ${apt.date}\n` +
                         `⏰ Turno: ${apt.shift}\n` +
                         `👨‍⚕️ Profesional: ${apt.professional.name}\n` +
                         `🏥 Box: ${apt.box.name}`;
            
            const encodedText = encodeURIComponent(text);
            window.open(`https://wa.me/?text=${encodedText}`, '_blank');
        }

        function shareViaEmailLegacy() {
            if (!state.selectedAppointment) return;
            
            const apt = state.selectedAppointment;
            const subject = encodeURIComponent('Turno médico agendado');
            const body = encodeURIComponent(
                `Turno médico agendado:\n\n` +
                `Día: ${apt.date}\n` +
                `Turno: ${apt.shift}\n` +
                `Profesional: ${apt.professional.name}\n` +
                `Box: ${apt.box.name}`
            );
            
            window.open(`mailto:?subject=${subject}&body=${body}`, '_blank');
        }

        // Utility functions
        function getAppointmentTypeLabel(type) {
            const types = {
                'consulta': 'Consulta General',
                'control': 'Control',
                'urgencia': 'Urgencia',
                'procedimiento': 'Procedimiento'
            };
            return types[type] || type;
        }

        function renderProfessionalKPIs() {
            const kpisContainer = document.getElementById('professionalKPIs');
            if (!kpisContainer) return;
            
            const professionalStats = calculateProfessionalKPIs();
            
            // Clear existing content
            kpisContainer.innerHTML = '';
            
            // Check if there are any professionals
            if (Object.keys(professionalStats).length === 0) {
                kpisContainer.innerHTML = '<div class="no-items">No hay profesionales registrados</div>';
                return;
            }
            
            // Sort professionals by total appointments (descending)
            const sortedProfessionals = Object.values(professionalStats)
                .sort((a, b) => b.totalCount - a.totalCount);
            
            sortedProfessionals.forEach(stats => {
                const kpiCard = document.createElement('div');
                kpiCard.className = 'professional-kpi-card';
                
                kpiCard.innerHTML = `
                    <div class="professional-name-kpi">${stats.abbreviatedName}</div>
                    <div class="kpi-metrics">
                        <div class="kpi-metric">
                            <div class="kpi-metric-value">${stats.amCount}</div>
                            <div class="kpi-metric-label">Turnos AM</div>
                        </div>
                        <div class="kpi-metric">
                            <div class="kpi-metric-value">${stats.pmCount}</div>
                            <div class="kpi-metric-label">Turnos PM</div>
                        </div>
                    </div>
                    <div class="kpi-percentages">
                        <div class="kpi-percentage">
                            <div class="kpi-percentage-value">${stats.percentage}%</div>
                            <div class="kpi-percentage-label">Del Total</div>
                        </div>
                        <div class="kpi-percentage">
                            <div class="kpi-percentage-value">${stats.attendancePercentage}%</div>
                            <div class="kpi-percentage-label">Asistencia</div>
                        </div>
                    </div>
                `;
                
                kpisContainer.appendChild(kpiCard);
            });
        }

        function updateStats() {
            // Update box statistics
            const totalBoxesEl = document.getElementById('totalBoxes');
            
            if (totalBoxesEl) totalBoxesEl.textContent = state.boxes.length;
            
            // Update box availability for today
            renderBoxAvailability();
            
            // Update professional KPIs
            renderProfessionalKPIs();
        }

        function renderBoxAvailability(selectedDate = null) {
            const container = document.getElementById('boxAvailabilityContainer');
            if (!container) return;

            // Use selected date or today's date in YYYY-MM-DD format
            let targetDate;
            if (selectedDate) {
                targetDate = selectedDate;
            } else {
                const today = new Date();
                targetDate = `${today.getFullYear()}-${String(today.getMonth() + 1).padStart(2, '0')}-${String(today.getDate()).padStart(2, '0')}`;
            }
            
            // Get appointments for the target date
            const dateAppointments = state.appointments.filter(apt => apt.date === targetDate);
            
            // Check availability for each box
            const boxAvailability = state.boxes.map(box => {
                const amAppointment = dateAppointments.find(apt => apt.box.id === box.id && apt.shift === 'AM');
                const pmAppointment = dateAppointments.find(apt => apt.box.id === box.id && apt.shift === 'PM');
                
                return {
                    box: box,
                    amAvailable: !amAppointment,
                    pmAvailable: !pmAppointment,
                    amAppointment: amAppointment,
                    pmAppointment: pmAppointment
                };
            });
            
            // Render all boxes with their availability status
            // Only show badges for available shifts
            const allBoxes = boxAvailability;
            container.innerHTML = allBoxes.map(item => {
                const shifts = [];
                
                // Only show badges for available shifts
                if (item.amAvailable) {
                    shifts.push('<span class="shift-badge available">AM</span>');
                }
                if (item.pmAvailable) {
                    shifts.push('<span class="shift-badge available">PM</span>');
                }
                
                // If no shifts are available, show a message or empty state
                const shiftsHTML = shifts.length > 0 ? 
                    `<div class="box-shifts-availability">${shifts.join('')}</div>` :
                    '<div class="box-shifts-availability"><span class="no-shifts-available">Sin turnos disponibles</span></div>';
                
                return `
                    <div class="box-availability-item">
                        <div class="box-name-availability">${item.box.name}</div>
                        ${shiftsHTML}
                    </div>
                `;
            }).join('');
        }

        // Modal open functions
        function openProfessionalsModal() {
            populateProfessionalManagement();
            openModal('professionalsModal');
        }

        // Responsive appointment modal function
        function openResponsiveAppointmentModal(appointment) {
            // Only execute in responsive mode
            if (window.innerWidth > 768) return;
            
            // Populate appointment modal with existing appointment data
            const appointmentDate = document.getElementById('appointmentDate');
            const appointmentShift = document.getElementById('appointmentShift');
            
            if (appointmentDate) {
                appointmentDate.value = appointment.date || '';
            }
            if (appointmentShift) {
                appointmentShift.value = appointment.shift || '';
            }
            
            // Populate professional dropdown if it exists
            const professionalDropdown = document.getElementById('professionalDropdown');
            if (professionalDropdown && appointment.professional) {
                const dropdownToggle = professionalDropdown.querySelector('.dropdown-toggle');
                if (dropdownToggle) {
                    dropdownToggle.textContent = appointment.professional.name || appointment.professional;
                }
            }
            
            // Modify modal footer for responsive mode
            const modalFooter = document.querySelector('#appointmentModal .modal-footer');
            if (modalFooter) {
                modalFooter.innerHTML = `
                    <button class="btn btn-secondary" id="cancelResponsiveAppointment">Cancelar</button>
                    <button class="btn btn-warning" id="modifyResponsiveAppointment">Modificar</button>
                    <button class="btn btn-danger" id="deleteResponsiveAppointment">Eliminar</button>
                `;
                
                // Add event listeners for responsive buttons
                const cancelBtn = document.getElementById('cancelResponsiveAppointment');
                const modifyBtn = document.getElementById('modifyResponsiveAppointment');
                const deleteBtn = document.getElementById('deleteResponsiveAppointment');
                
                if (cancelBtn) {
                    cancelBtn.addEventListener('click', function() {
                        closeModal('appointmentModal');
                        resetAppointmentModalFooter();
                    });
                }
                
                if (modifyBtn) {
                    modifyBtn.addEventListener('click', function() {
                        // Enable form fields for modification
                        enableAppointmentFormFields();
                        // Change buttons to save/cancel
                        modalFooter.innerHTML = `
                            <button class="btn btn-secondary" id="cancelModifyAppointment">Cancelar</button>
                            <button class="btn btn-primary" id="saveModifiedAppointment">💾 Guardar Cambios</button>
                        `;
                        
                        // Add save functionality
                        const saveBtn = document.getElementById('saveModifiedAppointment');
                        const cancelModifyBtn = document.getElementById('cancelModifyAppointment');
                        
                        if (saveBtn) {
                            saveBtn.addEventListener('click', function() {
                                // Save modified appointment logic here
                                saveAppointmentChanges(appointment);
                            });
                        }
                        
                        if (cancelModifyBtn) {
                            cancelModifyBtn.addEventListener('click', function() {
                                closeModal('appointmentModal');
                                resetAppointmentModalFooter();
                            });
                        }
                    });
                }
                
                if (deleteBtn) {
                    deleteBtn.addEventListener('click', function() {
                        if (confirm('¿Estás seguro de que deseas eliminar este turno?')) {
                            deleteAppointment(appointment);
                            closeModal('appointmentModal');
                            resetAppointmentModalFooter();
                        }
                    });
                }
            }
            
            // Open the modal
            openModal('appointmentModal');
        }

        // Helper function to reset modal footer to original state
        function resetAppointmentModalFooter() {
            const modalFooter = document.querySelector('#appointmentModal .modal-footer');
            if (modalFooter) {
                modalFooter.innerHTML = `
                    <button class="btn btn-secondary" id="cancelAppointment">Cancelar</button>
                    <button class="btn btn-primary" id="saveAppointment">💾 Guardar Turno</button>
                `;
            }
        }

        // Helper function to enable form fields for modification
        function enableAppointmentFormFields() {
            const appointmentShift = document.getElementById('appointmentShift');
            const professionalDropdown = document.getElementById('professionalDropdown');
            
            if (appointmentShift) {
                appointmentShift.disabled = false;
            }
            if (professionalDropdown) {
                const dropdownToggle = professionalDropdown.querySelector('.dropdown-toggle');
                if (dropdownToggle) {
                    dropdownToggle.disabled = false;
                }
            }
        }

        // Helper function to save appointment changes
        function saveAppointmentChanges(originalAppointment) {
            // Get updated values
            const appointmentShift = document.getElementById('appointmentShift');
            const updatedShift = appointmentShift ? appointmentShift.value : originalAppointment.shift;
            
            // Update appointment in state
            const appointmentIndex = state.appointments.findIndex(apt => apt.id === originalAppointment.id);
            if (appointmentIndex !== -1) {
                state.appointments[appointmentIndex].shift = updatedShift;
                // Add other field updates as needed
            }
            
            // Refresh calendar and close modal
            if (typeof renderCalendar === 'function') {
                renderCalendar();
            } else if (typeof updateCalendarForMonth === 'function') {
                updateCalendarForMonth(state.currentMonth, state.currentYear);
            }
            
            closeModal('appointmentModal');
            resetAppointmentModalFooter();
            
            // Show success message
            alert('Turno modificado exitosamente');
        }

        // Helper function to delete appointment
        function deleteAppointment(appointment) {
            // Remove appointment from state
            const appointmentIndex = state.appointments.findIndex(apt => apt.id === appointment.id);
            if (appointmentIndex !== -1) {
                state.appointments.splice(appointmentIndex, 1);
            }
            
            // Refresh calendar
            if (typeof renderCalendar === 'function') {
                renderCalendar();
            } else if (typeof updateCalendarForMonth === 'function') {
                updateCalendarForMonth(state.currentMonth, state.currentYear);
            }
            
            // Show success message
            alert('Turno eliminado exitosamente');
        }

        function openBoxesModal() {
            populateBoxManagement();
            openModal('boxesModal');
        }

        function openSettingsModal() {
            // Load current settings
            document.getElementById('defaultDuration').value = state.settings.defaultDuration;
            document.getElementById('startTime').value = state.settings.startTime;
            document.getElementById('endTime').value = state.settings.endTime;
            
            const checkboxes = document.querySelectorAll('#settingsModal input[type="checkbox"]');
            checkboxes[0].checked = state.settings.notifications.newAppointments;
            checkboxes[1].checked = state.settings.notifications.reminders;
            checkboxes[2].checked = state.settings.notifications.cancellations;
            
            openModal('settingsModal');
        }

        // Box Management Modal Functions
        function openBoxManagementModal() {
            populateBoxSelects();
            resetBoxManagementForm();
            openModal('boxManagementModal');
            const month = today.getMonth();
            const day = today.getDate();
            
            const dateStr = `${year}-${String(month + 1).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
            
            state.appointments = [
                {
                    id: 1,
                    date: dateStr,
                    shift: 'AM',
                    professional: state.professionals[0],
                    box: state.boxes[0],
                    status: 'pending'
                },
                {
                    id: 2,
                    date: dateStr,
                    shift: 'PM',
                    professional: state.professionals[1],
                    box: state.boxes[1],
                    status: 'completed'
                },
                {
                    id: 3,
                    date: dateStr,
                    shift: 'AM',
                    professional: state.professionals[2],
                    box: state.boxes[2],
                    status: 'pending'
                },
                {
                    id: 4,
                    date: dateStr,
                    shift: 'PM',
                    professional: state.professionals[0],
                    box: state.boxes[3],
                    status: 'pending'
                },
                {
                    id: 5,
                    date: dateStr,
                    shift: 'AM',
                    professional: state.professionals[0],
                    box: state.boxes[0],
                    status: 'cancelled'
                }
            ];
            
            renderCalendar();
            updateStats();
        }

        // Box Management Modal Functions
        function openBoxManagementModal() {
            populateBoxSelects();
            resetBoxManagementForm();
            openModal('boxManagementModal');
        }

        function populateBoxSelects() {
            const modifySelect = document.getElementById('modifyBoxSelect');
            const deleteSelect = document.getElementById('deleteBoxSelect');
            
            // Clear existing options
            modifySelect.innerHTML = '<option value="">Seleccionar box a modificar</option>';
            deleteSelect.innerHTML = '<option value="">Seleccionar box a eliminar</option>';
            
            // Populate with current boxes
            state.boxes.forEach(box => {
                const modifyOption = document.createElement('option');
                modifyOption.value = box.id;
                modifyOption.textContent = box.name;
                modifySelect.appendChild(modifyOption);
                
                const deleteOption = document.createElement('option');
                deleteOption.value = box.id;
                deleteOption.textContent = box.name;
                deleteSelect.appendChild(deleteOption);
            });
        }

        function resetBoxManagementForm() {
            document.getElementById('newBoxName').value = '';
            document.getElementById('modifyBoxSelect').value = '';
            document.getElementById('modifyBoxName').value = '';
            document.getElementById('deleteBoxSelect').value = '';
            
            // Reset to first tab
            switchTab('add-box');
        }

        function switchTab(tabId) {
            console.log('Switching to tab:', tabId); // Debug log
            
            // Remove active class from all tabs and contents within the box management modal
            document.querySelectorAll('#boxManagementModal .tab-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            document.querySelectorAll('#boxManagementModal .tab-content').forEach(content => {
                content.classList.remove('active');
            });
            
            // Add active class to selected tab and content
            const selectedTabBtn = document.querySelector(`#boxManagementModal [data-tab="${tabId}"]`);
            const selectedTabContent = document.getElementById(tabId);
            
            if (selectedTabBtn && selectedTabContent) {
                selectedTabBtn.classList.add('active');
                selectedTabContent.classList.add('active');
                console.log('Tab switched successfully to:', tabId); // Debug log
            } else {
                console.error('Tab or content not found:', tabId); // Debug log
            }
        }

        function addNewBox() {
            const boxName = document.getElementById('newBoxName').value.trim();
            
            if (!boxName) {
                alert('Por favor ingrese un nombre para el box');
                return;
            }
            
            // Check if box name already exists
            if (state.boxes.some(box => box.name.toLowerCase() === boxName.toLowerCase())) {
                alert('Ya existe un box con ese nombre');
                return;
            }
            
            const newBox = {
                id: Date.now(),
                name: boxName,
                location: 'Por definir',
                equipment: 'Por definir',
                status: 'available'
            };
            
            state.boxes.push(newBox);
            
            // Update all displays
            populateBoxes();
            populateBoxSelects();
            updateStats();
            renderCalendar();
            
            // Clear form and close modal
            document.getElementById('newBoxName').value = '';
            closeModal('boxManagementModal');
            
            alert(`Box "${boxName}" agregado correctamente`);
        }

        function modifySelectedBox() {
            const boxId = parseInt(document.getElementById('modifyBoxSelect').value);
            const newName = document.getElementById('modifyBoxName').value.trim();
            
            if (!boxId) {
                alert('Por favor seleccione un box para modificar');
                return;
            }
            
            if (!newName) {
                alert('Por favor ingrese el nuevo nombre del box');
                return;
            }
            
            // Check if new name already exists (excluding current box)
            if (state.boxes.some(box => box.id !== boxId && box.name.toLowerCase() === newName.toLowerCase())) {
                alert('Ya existe un box con ese nombre');
                return;
            }
            
            const box = state.boxes.find(b => b.id === boxId);
            if (box) {
                const oldName = box.name;
                box.name = newName;
                
                // Update appointments from today forward
                const today = new Date().toISOString().split('T')[0];
                state.appointments.forEach(appointment => {
                    if (appointment.date >= today && appointment.box.id === boxId) {
                        appointment.box.name = newName;
                    }
                });
                
                // Update all displays
                populateBoxes();
                populateBoxSelects();
                updateStats();
                renderCalendar();
                
                // Clear form and close modal
                document.getElementById('modifyBoxSelect').value = '';
                document.getElementById('modifyBoxName').value = '';
                closeModal('boxManagementModal');
                
                alert(`Box "${oldName}" modificado a "${newName}" correctamente`);
            }
        }

        function deleteSelectedBox() {
            const boxId = parseInt(document.getElementById('deleteBoxSelect').value);
            
            if (!boxId) {
                alert('Por favor seleccione un box para eliminar');
                return;
            }
            
            const box = state.boxes.find(b => b.id === boxId);
            if (!box) {
                alert('Box no encontrado');
                return;
            }
            
            // Check for future appointments
            const today = new Date().toISOString().split('T')[0];
            const futureAppointments = state.appointments.filter(apt => 
                apt.date >= today && apt.box.id === boxId
            );
            
            let confirmMessage = `¿Está seguro de que desea eliminar el box "${box.name}"?`;
            if (futureAppointments.length > 0) {
                confirmMessage += `\n\nEsto eliminará ${futureAppointments.length} cita(s) programada(s) desde hoy en adelante.`;
            }
            
            if (confirm(confirmMessage)) {
                // Remove box from state
                state.boxes = state.boxes.filter(b => b.id !== boxId);
                
                // Remove future appointments with this box
                state.appointments = state.appointments.filter(apt => 
                    !(apt.date >= today && apt.box.id === boxId)
                );
                
                // Update all displays
                populateBoxes();
                populateBoxSelects();
                updateStats();
                renderCalendar();
                
                // Clear form and close modal
                document.getElementById('deleteBoxSelect').value = '';
                closeModal('boxManagementModal');
                
                alert(`Box "${box.name}" eliminado correctamente`);
            }
        }

        // Modal Management Functions
        function openModal(modalId) {
            const modal = document.getElementById(modalId);
            if (modal) {
                modal.style.display = 'flex';
                modal.classList.add('show');
                // Prevent body scroll when modal is open
                document.body.style.overflow = 'hidden';
            }
        }

        function closeModal(modalId) {
            const modal = document.getElementById(modalId);
            if (modal) {
                modal.style.display = 'none';
                modal.classList.remove('show');
                // Restore body scroll
                document.body.style.overflow = 'auto';
            }
        }

        // Specific function for closing box management modal
        function closeBoxModal() {
            const modal = document.getElementById('boxManagementModal');
            if (modal) {
                modal.style.display = 'none';
                modal.classList.remove('show');
                document.body.style.overflow = 'auto';
                // Reset form
                document.getElementById('newBoxName').value = '';
                // Switch back to add tab
                switchTab('add-box');
            }
        }

        // Specific function for closing professionals modal
        function closeProfessionalsModal() {
            const modal = document.getElementById('professionalsModal');
            if (modal) {
                modal.style.display = 'none';
                modal.classList.remove('show');
                document.body.style.overflow = 'auto';
                // Reset form
                resetProfessionalManagementForm();
            }
        }

        // Function to populate the professional list in sidebar
        function populateProfessionalList() {
            const professionalList = document.getElementById('professionalList');
            if (!professionalList) return;

            // Filter active professionals
            const activeProfessionals = state.professionals.filter(prof => prof.status === 'Vigente');

            if (activeProfessionals.length === 0) {
                professionalList.innerHTML = '<div class="no-items">No hay profesionales activos</div>';
                return;
            }

            professionalList.innerHTML = activeProfessionals.map(professional => `
                <div class="professional-item">
                    <div class="professional-avatar">${professional.avatar || professional.name.charAt(0)}</div>
                    <div class="professional-info">
                        <div class="professional-name">${professional.name}</div>
                        <div class="professional-specialty">${professional.specialty}</div>
                    </div>
                    <div class="professional-status active">●</div>
                </div>
            `).join('');
        }

        // Function to populate the professional list in sidebar
        function populateProfessionalList() {
            const professionalList = document.getElementById('professionalList');
            if (!professionalList) return;

            // Filter active professionals
            const activeProfessionals = state.professionals.filter(prof => prof.status === 'active');

            if (activeProfessionals.length === 0) {
                professionalList.innerHTML = '<div class="no-items">No hay profesionales activos</div>';
                return;
            }

            professionalList.innerHTML = activeProfessionals.map(professional => `
                <div class="professional-item">
                    <div class="professional-avatar">${professional.avatar || professional.name.charAt(0)}</div>
                    <div class="professional-info">
                        <div class="professional-name">${professional.name}</div>
                        <div class="professional-specialty">${professional.specialty}</div>
                    </div>
                    <div class="professional-status active">●</div>
                </div>
            `).join('');
        }

        // Function to display professional list in sidebar (fixed version)
        function displayProfessionalList() {
            try {
                console.log('displayProfessionalList called'); // Debug log
                
                const professionalListElement = document.getElementById('professionalList');
                if (!professionalListElement) {
                    console.error('professionalList element not found');
                    return;
                }

                // Check if state and professionals exist
                if (!state || !state.professionals) {
                    console.error('State or professionals not available', state);
                    professionalListElement.innerHTML = '<div class="no-items">No hay profesionales disponibles</div>';
                    return;
                }

                console.log('Current professionals:', state.professionals); // Debug log

                // Show ALL professionals with their status (not just active ones)
                const allProfessionals = state.professionals.filter(prof => prof && prof.name);

                console.log('Filtered professionals:', allProfessionals); // Debug log

                if (allProfessionals.length === 0) {
                    console.log('No professionals found');
                    professionalListElement.innerHTML = '<div class="no-items">No hay profesionales registrados</div>';
                    return;
                }

                // Generate HTML for each professional
                let htmlContent = '';
                for (let i = 0; i < allProfessionals.length; i++) {
                    const prof = allProfessionals[i];
                    
                    // Validate professional data
                    if (!prof.name || !prof.status) {
                        console.warn('Professional with incomplete data:', prof);
                        continue;
                    }
                    
                    const avatarText = prof.avatar || prof.name.charAt(0).toUpperCase();
                    const statusClass = prof.status === 'Vigente' ? 'vigente' : 'no-vigente';
                    const statusColor = prof.status === 'Vigente' ? '#10b981' : '#ef4444';
                    
                    // Format date range if professional has status dates
                    let statusText = prof.status;
                    if (prof.status !== 'Vigente' && prof.statusFromDate && prof.statusToDate) {
                        const fromDate = new Date(prof.statusFromDate).toLocaleDateString('es-ES', { 
                            day: '2-digit', 
                            month: '2-digit' 
                        });
                        const toDate = new Date(prof.statusToDate).toLocaleDateString('es-ES', { 
                            day: '2-digit', 
                            month: '2-digit' 
                        });
                        statusText += `<br><span style="font-size: 10px; opacity: 0.8;">${fromDate} - ${toDate}</span>`;
                    }
                    
                    console.log(`Rendering professional: ${prof.name} - Status: ${prof.status}`); // Debug log
                    
                    htmlContent += `
                <div class="professional-item">
                    <div class="professional-avatar">${avatarText}</div>
                    <div class="professional-info">
                        <div class="professional-name">${prof.name}</div>
                        <div class="professional-status-text" style="font-size: 12px; color: ${statusColor}; font-weight: 500; line-height: 1.3;">${statusText}</div>
                    </div>
                    <div class="professional-status ${statusClass}" style="color: ${statusColor};">●</div>
                </div>
            `;
                }
                
                console.log('Generated HTML content length:', htmlContent.length); // Debug log
                
                professionalListElement.innerHTML = htmlContent;
                console.log('Professional list displayed successfully with', allProfessionals.length, 'professionals');
            } catch (error) {
                console.error('Error in displayProfessionalList:', error);
                // Try to show error message to user
                const professionalListElement = document.getElementById('professionalList');
                if (professionalListElement) {
                    professionalListElement.innerHTML = '<div class="no-items" style="color: red;">Error al cargar profesionales</div>';
                }
            }
        }



        // Professional Management Functions
        function openProfessionalsModal() {
            populateProfessionalSelects();
            populateModifyStatusProfessionalSelect();
            resetProfessionalManagementForm();
            openModal('professionalsModal');
        }

        // Function to populate professional select for modify status tab
        function populateModifyStatusProfessionalSelect() {
            const select = document.getElementById('modifyStatusProfessionalSelect');
            if (!select) return;
            
            // Clear existing options except the first one
            select.innerHTML = '<option value="">Seleccionar profesional</option>';
            
            // Add all professionals to the select
            state.professionals.forEach(professional => {
                const option = document.createElement('option');
                option.value = professional.id;
                option.textContent = `${professional.name} (${professional.status})`;
                select.appendChild(option);
            });
        }

        // Function to enable status select when professional is selected
        function enableStatusSelect() {
            const professionalSelect = document.getElementById('modifyStatusProfessionalSelect');
            const statusSelect = document.getElementById('modifyStatusSelect');
            
            if (professionalSelect.value) {
                statusSelect.disabled = false;
                
                // Find selected professional and set current status
                const selectedProfessional = state.professionals.find(prof => prof.id == professionalSelect.value);
                if (selectedProfessional) {
                    statusSelect.value = selectedProfessional.status;
                    
                    // Show date range if professional has status dates
                    if (selectedProfessional.statusFromDate && selectedProfessional.statusToDate && selectedProfessional.status !== 'Vigente') {
                        document.getElementById('statusFromDate').value = selectedProfessional.statusFromDate;
                        document.getElementById('statusToDate').value = selectedProfessional.statusToDate;
                        document.getElementById('dateRangeGroup').style.display = 'block';
                    } else {
                        handleStatusChange();
                    }
                }
            } else {
                statusSelect.disabled = true;
                statusSelect.value = '';
                document.getElementById('dateRangeGroup').style.display = 'none';
            }
        }

        // Function to handle status change and toggle date range visibility
        function handleStatusChange() {
            console.log('handleStatusChange called'); // Debug log
            
            // Use setTimeout to ensure DOM is ready
            setTimeout(() => {
                const statusSelect = document.getElementById('modifyStatusSelect');
                const dateRangeGroup = document.getElementById('dateRangeGroup');
                
                console.log('Status selected:', statusSelect ? statusSelect.value : 'statusSelect not found'); // Debug log
                console.log('DateRangeGroup found:', dateRangeGroup ? 'yes' : 'no'); // Debug log
                
                if (!statusSelect || !dateRangeGroup) {
                    console.error('Required elements not found');
                    return;
                }
                
                if (statusSelect.value && statusSelect.value !== 'Vigente') {
                    console.log('Showing date range for status:', statusSelect.value); // Debug log
                    dateRangeGroup.style.display = 'block';
                    
                    // Set default dates if empty
                    const fromDate = document.getElementById('statusFromDate');
                    const toDate = document.getElementById('statusToDate');
                    
                    if (fromDate && !fromDate.value) {
                        const today = new Date();
                        fromDate.value = today.toISOString().split('T')[0];
                        console.log('Set default from date:', fromDate.value); // Debug log
                    }
                    
                    if (toDate && !toDate.value) {
                        const nextWeek = new Date();
                        nextWeek.setDate(nextWeek.getDate() + 7);
                        toDate.value = nextWeek.toISOString().split('T')[0];
                        console.log('Set default to date:', toDate.value); // Debug log
                    }
                } else {
                    console.log('Hiding date range'); // Debug log
                    dateRangeGroup.style.display = 'none';
                    
                    // Clear date values
                    const fromDate = document.getElementById('statusFromDate');
                    const toDate = document.getElementById('statusToDate');
                    if (fromDate) fromDate.value = '';
                    if (toDate) toDate.value = '';
                }
            }, 10);
        }

        // Keep the old function for backward compatibility
        function toggleDateRange() {
            handleStatusChange();
        }

        

        // Function to modify professional status
        async function modifyProfessionalStatus() {
            const professionalId = document.getElementById('modifyStatusProfessionalSelect').value;
            const newStatus = document.getElementById('modifyStatusSelect').value;
            const fromDate = document.getElementById('statusFromDate').value;
            const toDate = document.getElementById('statusToDate').value;
            
            if (!professionalId) {
                alert('Por favor seleccione un profesional');
                return;
            }
            
            if (!newStatus) {
                alert('Por favor seleccione un estado');
                return;
            }
            
            // Validate date range if not "Vigente"
            if (newStatus !== 'Vigente') {
                if (!fromDate || !toDate) {
                    alert('Por favor seleccione el rango de fechas para el estado no vigente');
                    return;
                }
                
                if (new Date(fromDate) >= new Date(toDate)) {
                    alert('La fecha "Desde" debe ser anterior a la fecha "Hasta"');
                    return;
                }
            }
            
            // Llamar backend para persistir
            try {
                const formData = new FormData();
                formData.append('action', 'update_status');
                formData.append('id', professionalId);
                formData.append('estado', newStatus);
                if (newStatus !== 'Vigente') {
                    formData.append('estado_desde', fromDate);
                    formData.append('estado_hasta', toDate);
                } else {
                    formData.append('estado_desde', '');
                    formData.append('estado_hasta', '');
                }
                await apiRequest('api/professionals.php', {
                    method: 'POST',
                    body: formData
                });
                // Actualizar estado local
                const idx = state.professionals.findIndex(prof => prof.id == professionalId);
                if (idx !== -1) {
                    const oldStatus = state.professionals[idx].status;
                    const professionalName = state.professionals[idx].name;
                    state.professionals[idx].status = newStatus;
                    state.professionals[idx].statusFromDate = newStatus !== 'Vigente' ? fromDate : '';
                    state.professionals[idx].statusToDate = newStatus !== 'Vigente' ? toDate : '';

                    // Refrescar UI
                    displayProfessionalList();
                    populateModifyStatusProfessionalSelect();
                    populateProfessionals();
                    populateDetailsProfessionals();
                    updateStats();

                    // Reset form
                    document.getElementById('modifyStatusProfessionalSelect').value = '';
                    document.getElementById('modifyStatusSelect').value = '';
                    document.getElementById('modifyStatusSelect').disabled = true;
                    document.getElementById('statusFromDate').value = '';
                    document.getElementById('statusToDate').value = '';
                    document.getElementById('dateRangeGroup').style.display = 'none';

                    closeModal('professionalsModal');

                    let successMessage = `Estado del profesional "${professionalName}" actualizado de "${oldStatus}" a "${newStatus}"`;
                    if (newStatus !== 'Vigente' && fromDate && toDate) {
                        const fromDateFormatted = new Date(fromDate).toLocaleDateString('es-ES');
                        const toDateFormatted = new Date(toDate).toLocaleDateString('es-ES');
                        successMessage += ` desde ${fromDateFormatted} hasta ${toDateFormatted}`;
                    }
                    alert(successMessage);
                } else {
                    alert('Error: Profesional no encontrado');
                }
            } catch (_) {
                // Error ya mostrado por apiRequest
            }
        }

        // Function to switch professional tabs
        function switchProfessionalTab(tabId) {
            // Remove active class from all tabs and contents
            document.querySelectorAll('#professionalsModal .tab-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            document.querySelectorAll('#professionalsModal .tab-content').forEach(content => {
                content.classList.remove('active');
            });
            
            // Add active class to selected tab and content
            const selectedTabBtn = document.querySelector(`#professionalsModal [data-tab="${tabId}"]`);
            const selectedTabContent = document.getElementById(tabId);
            
            if (selectedTabBtn && selectedTabContent) {
                selectedTabBtn.classList.add('active');
                selectedTabContent.classList.add('active');
                
                // If switching to modify status tab, populate the select
                if (tabId === 'modify-status') {
                    populateModifyStatusProfessionalSelect();
                }
            }
        }

        function resetProfessionalManagementForm() {
            // Reset add professional tab
            document.getElementById('newProfessionalName').value = '';
            
            // Reset delete professional tab
            document.getElementById('deleteProfessionalSelect').value = '';
            
            // Switch to add professional tab by default
            switchProfessionalTab('add-professional');
        }

        function switchProfessionalTab(tabId) {
            console.log('Switching to professional tab:', tabId);
            
            // Remove active class from all tabs and contents within the professionals modal
            document.querySelectorAll('#professionalsModal .tab-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            document.querySelectorAll('#professionalsModal .tab-content').forEach(content => {
                content.classList.remove('active');
            });
            
            // Add active class to selected tab and content
            const selectedTabBtn = document.querySelector(`#professionalsModal [data-tab="${tabId}"]`);
            const selectedTabContent = document.getElementById(tabId);
            
            if (selectedTabBtn && selectedTabContent) {
                selectedTabBtn.classList.add('active');
                selectedTabContent.classList.add('active');
                
                // Populate professional select when switching to delete tab
                if (tabId === 'delete-professional') {
                    populateProfessionalSelects();
                }
            }
        }

        function populateProfessionalSelects() {
            const deleteSelect = document.getElementById('deleteProfessionalSelect');
            if (!deleteSelect) return;
            
            deleteSelect.innerHTML = '<option value="">Seleccionar profesional a eliminar</option>';
            
            state.professionals.forEach(professional => {
                const option = document.createElement('option');
                option.value = professional.id;
                option.textContent = professional.name;
                deleteSelect.appendChild(option);
            });
        }

        async function addNewProfessional() {
            const professionalName = document.getElementById('newProfessionalName').value.trim();
            
            if (!professionalName) {
                alert('Por favor ingrese el nombre del profesional');
                return;
            }
            
            // Validación local de duplicados por nombre
            if (state.professionals.some(prof => prof.name.toLowerCase() === professionalName.toLowerCase())) {
                alert('Ya existe un profesional con ese nombre');
                return;
            }
            
            try {
                const formData = new FormData();
                formData.append('action', 'create');
                formData.append('nombre', professionalName);
                const res = await apiRequest('api/professionals.php', {
                    method: 'POST',
                    body: formData
                });
                const newId = parseInt(res.id, 10);
                const newProfessional = {
                    id: newId,
                    name: professionalName,
                    email: '',
                    phone: '',
                    avatar: '👨‍⚕️',
                    status: 'Vigente'
                };
                state.professionals.push(newProfessional);

                // Update all displays
                displayProfessionalList();
                populateProfessionals();
                populateProfessionalSelects();
                populateProfessionalManagement();
                updateStats();
                renderCalendar();
                
                setTimeout(() => {
                    const professionalListElement = document.getElementById('professionalList');
                    if (professionalListElement) {
                        professionalListElement.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
                    }
                }, 200);
                
                document.getElementById('newProfessionalName').value = '';
                closeModal('professionalsModal');
                alert(`Profesional "${professionalName}" agregado correctamente`);
            } catch (_) {
                // Error ya mostrado en apiRequest
            }
        }

        function toggleDateRange() {
            const deleteType = document.querySelector('input[name="deleteType"]:checked');
            const dateRangeGroup = document.getElementById('dateRangeGroup');
            
            if (deleteType && deleteType.value === 'dateRange') {
                dateRangeGroup.style.display = 'block';
                // Set default dates (today to 30 days from now)
                const today = new Date();
                const futureDate = new Date();
                futureDate.setDate(today.getDate() + 30);
                
                document.getElementById('deleteFromDate').value = today.toISOString().split('T')[0];
                document.getElementById('deleteToDate').value = futureDate.toISOString().split('T')[0];
            } else {
                dateRangeGroup.style.display = 'none';
            }
        }

        async function deleteSelectedProfessional() {
            const professionalId = parseInt(document.getElementById('deleteProfessionalSelect').value);
            
            if (!professionalId) {
                alert('Por favor seleccione un profesional para eliminar');
                return;
            }
            
            const professional = state.professionals.find(p => p.id === professionalId);
            if (!professional) {
                alert('Profesional no encontrado');
                return;
            }
            
            // Get today's date for filtering future appointments
            const today = new Date().toISOString().split('T')[0];
            const futureAppointments = state.appointments.filter(apt => 
                apt.date >= today && apt.professional && apt.professional.id === professionalId
            );
            
            let confirmMessage = `¿Está seguro de que desea eliminar al profesional "${professional.name}"?`;
            if (futureAppointments.length > 0) {
                confirmMessage += `\n\nEsto eliminará ${futureAppointments.length} turno(s) programado(s) desde hoy en adelante.`;
            }
            confirmMessage += `\n\nEl profesional también será removido de la lista de opciones para nuevos turnos.`;
            
            if (confirm(confirmMessage)) {
                try {
                    const formData = new FormData();
                    formData.append('action', 'delete');
                    formData.append('id', professionalId);
                    await apiRequest('api/professionals.php', {
                        method: 'POST',
                        body: formData
                    });
                    // Quitar del estado local
                    state.professionals = state.professionals.filter(p => p.id !== professionalId);
                    state.appointments = state.appointments.filter(apt => 
                        !(apt.date >= today && apt.professional && apt.professional.id === professionalId)
                    );
                    updateDisplaysAndCloseModal('professionalsModal');
                    alert(`Profesional "${professional.name}" eliminado exitosamente`);
                } catch (_) {
                    // Error ya mostrado
                }
            }
        }

        function updateDisplaysAndCloseModal(modalId) {
            // Update all displays
            displayProfessionalList();
            populateProfessionals();
            populateDetailsProfessionals();
            populateProfessionalSelects();
            updateStats();
            renderCalendar();
            
            // Clear form and close modal
            document.getElementById('deleteProfessionalSelect').value = '';
            closeModal(modalId);
        }



        // Initialize the calendar when DOM is loaded
        document.addEventListener('DOMContentLoaded', initCalendar);
        
        // Additional initialization to ensure professional list and KPIs are always displayed
        document.addEventListener('DOMContentLoaded', function() {
            // Multiple attempts to ensure the professional list is displayed
            setTimeout(() => displayProfessionalList(), 200);
            setTimeout(() => displayProfessionalList(), 500);
            setTimeout(() => displayProfessionalList(), 1000);
            
            // Ensure KPIs are always visible on page load
            setTimeout(() => renderProfessionalKPIs(), 300);
            setTimeout(() => renderProfessionalKPIs(), 600);
            setTimeout(() => renderProfessionalKPIs(), 1200);
            
            // Initialize dropdown functionality for professional selector in appointment modal
            const professionalDropdown = document.getElementById('professionalDropdown');
            const professionalToggle = professionalDropdown?.querySelector('.dropdown-toggle');
            
            if (professionalToggle) {
                professionalToggle.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    
                    // Toggle dropdown active state
                    professionalDropdown.classList.toggle('active');
                    
                    // Close other dropdowns if any
                    document.querySelectorAll('.dropdown').forEach(dropdown => {
                        if (dropdown !== professionalDropdown) {
                            dropdown.classList.remove('active');
                        }
                    });
                });
            }
            
            // Close dropdown when clicking outside
            document.addEventListener('click', function(e) {
                if (!professionalDropdown?.contains(e.target)) {
                    professionalDropdown?.classList.remove('active');
                }
            });
            
            // Mobile Navigation functionality
            const mobileNavBtns = document.querySelectorAll('.mobile-nav-btn');
            
            mobileNavBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    const targetSection = this.getAttribute('data-section');
                    
                    // Remove active class from all buttons
                    mobileNavBtns.forEach(b => b.classList.remove('active'));
                    // Add active class to clicked button
                    this.classList.add('active');
                    
                    // Hide all sections first
                    document.getElementById('mobile-calendar-section').style.display = 'none';
                    document.getElementById('mobile-calendar-section-content').style.display = 'none';
                    document.querySelectorAll('.mobile-section').forEach(section => {
                        section.style.display = 'none';
                    });
                    
                    // Show the selected section
                    if (targetSection === 'calendar') {
                        document.getElementById('mobile-calendar-section').style.display = 'block';
                        document.getElementById('mobile-calendar-section-content').style.display = 'block';
                    } else if (targetSection === 'professionals') {
                        document.getElementById('mobile-professionals-section').style.display = 'block';
                        syncMobileData('professionals');
                    } else if (targetSection === 'boxes') {
                        document.getElementById('mobile-boxes-section').style.display = 'block';
                        syncMobileData('boxes');
                    } else if (targetSection === 'kpis') {
                        document.getElementById('mobile-kpis-section').style.display = 'block';
                        syncMobileData('kpis');
                    }
                });
            });
            
            // Function to sync data between desktop and mobile sections
            function syncMobileData(sectionType) {
                if (sectionType === 'professionals') {
                    const professionalList = document.getElementById('professionalList');
                    const professionalListMobile = document.getElementById('professionalListMobile');
                    if (professionalList && professionalListMobile) {
                        professionalListMobile.innerHTML = professionalList.innerHTML;
                    }
                } else if (sectionType === 'boxes') {
                    const boxContainer = document.getElementById('boxAvailabilityContainer');
                    const boxContainerMobile = document.getElementById('boxAvailabilityContainerMobile');
                    if (boxContainer && boxContainerMobile) {
                        boxContainerMobile.innerHTML = boxContainer.innerHTML;
                    }
                    
                    const totalBoxes = document.getElementById('totalBoxes');
                    const totalBoxesMobile = document.getElementById('totalBoxesMobile');
                    if (totalBoxes && totalBoxesMobile) {
                        totalBoxesMobile.textContent = totalBoxes.textContent;
                    }
                } else if (sectionType === 'kpis') {
                    const professionalKPIs = document.getElementById('professionalKPIs');
                    const professionalKPIsMobile = document.getElementById('professionalKPIsMobile');
                    if (professionalKPIs && professionalKPIsMobile) {
                        professionalKPIsMobile.innerHTML = professionalKPIs.innerHTML;
                    }
                }
            }
            
            // Initial sync of all mobile data
            setTimeout(() => {
                syncMobileData('professionals');
                syncMobileData('boxes');
                syncMobileData('kpis');
            }, 1000);
            
            // Mobile Calendar Arrow Navigation functionality
            const currentMonthElement = document.getElementById('currentMonth');
            const prevButton = document.getElementById('prevMonth');
            const nextButton = document.getElementById('nextMonth');
            
            // Initialize mobile calendar state
            let mobileCurrentMonth = new Date().getMonth();
            let mobileCurrentYear = new Date().getFullYear();
            
            // Add event listeners for navigation arrows (only on mobile)
            if (window.innerWidth <= 768) {
                if (prevButton) {
                    prevButton.addEventListener('click', function() {
                        mobileCurrentMonth--;
                        if (mobileCurrentMonth < 0) {
                            mobileCurrentMonth = 11;
                            mobileCurrentYear--;
                        }
                        updateMobileCalendar();
                    });
                }
                
                if (nextButton) {
                    nextButton.addEventListener('click', function() {
                        mobileCurrentMonth++;
                        if (mobileCurrentMonth > 11) {
                            mobileCurrentMonth = 0;
                            mobileCurrentYear++;
                        }
                        updateMobileCalendar();
                    });
                }
            }
            
            // Function to update mobile calendar
            function updateMobileCalendar() {
                const monthNames = [
                    'Enero', 'Febrero', 'Marzo', 'Abril', 'Mayo', 'Junio',
                    'Julio', 'Agosto', 'Septiembre', 'Octubre', 'Noviembre', 'Diciembre'
                ];
                
                // Update month display
                if (currentMonthElement) {
                    currentMonthElement.textContent = `${monthNames[mobileCurrentMonth]} ${mobileCurrentYear}`;
                }
                
                // Update calendar for selected month
                updateCalendarForMonth(mobileCurrentMonth, mobileCurrentYear);
                
                // Update all sections with data for selected month
                updateSectionsForMonth(mobileCurrentMonth, mobileCurrentYear);
            }
            
            // Mobile Calendar Touch/Swipe functionality
            if (window.innerWidth <= 768) {
                // Force calendar generation for responsive mode
                setTimeout(() => {
                    updateCalendarForMonth(mobileCurrentMonth, mobileCurrentYear);
                }, 100);
                
                const calendarContainer = document.querySelector('.calendar-container');
                let startX = 0;
                let startY = 0;
                let scrollLeft = 0;
                let scrollTop = 0;
                let isDown = false;
                
                if (calendarContainer) {
                    // Touch events for mobile - supporting both horizontal and vertical scroll
                    calendarContainer.addEventListener('touchstart', function(e) {
                        isDown = true;
                        startX = e.touches[0].pageX - calendarContainer.offsetLeft;
                        startY = e.touches[0].pageY - calendarContainer.offsetTop;
                        scrollLeft = calendarContainer.scrollLeft;
                        scrollTop = calendarContainer.scrollTop;
                    });
                    
                    calendarContainer.addEventListener('touchmove', function(e) {
                        if (!isDown) return;
                        
                        const x = e.touches[0].pageX - calendarContainer.offsetLeft;
                        const y = e.touches[0].pageY - calendarContainer.offsetTop;
                        
                        const walkX = (x - startX) * 2; // Horizontal scroll speed multiplier
                        const walkY = (y - startY) * 2; // Vertical scroll speed multiplier
                        
                        // Determine if the movement is more horizontal or vertical
                        const deltaX = Math.abs(x - startX);
                        const deltaY = Math.abs(y - startY);
                        
                        if (deltaX > deltaY) {
                            // Horizontal scroll (existing functionality)
                            e.preventDefault();
                            calendarContainer.scrollLeft = scrollLeft - walkX;
                        } else {
                            // Vertical scroll (new functionality)
                            e.preventDefault();
                            calendarContainer.scrollTop = scrollTop - walkY;
                        }
                    });
                    
                    calendarContainer.addEventListener('touchend', function() {
                        isDown = false;
                    });
                    
                    // Mouse events for desktop testing
                    calendarContainer.addEventListener('mousedown', function(e) {
                        isDown = true;
                        calendarContainer.style.cursor = 'grabbing';
                        startX = e.pageX - calendarContainer.offsetLeft;
                        scrollLeft = calendarContainer.scrollLeft;
                    });
                    
                    calendarContainer.addEventListener('mouseleave', function() {
                        isDown = false;
                        calendarContainer.style.cursor = 'grab';
                    });
                    
                    calendarContainer.addEventListener('mouseup', function() {
                        isDown = false;
                        calendarContainer.style.cursor = 'grab';
                    });
                    
                    calendarContainer.addEventListener('mousemove', function(e) {
                        if (!isDown) return;
                        e.preventDefault();
                        const x = e.pageX - calendarContainer.offsetLeft;
                        const walk = (x - startX) * 2;
                        calendarContainer.scrollLeft = scrollLeft - walk;
                    });
                    
                    // Add grab cursor
                    calendarContainer.style.cursor = 'grab';
                    
                    // Smooth scroll to show first day on load
                    setTimeout(() => {
                        calendarContainer.scrollLeft = 0;
                    }, 500);
                }
            }
            
            // Function to update calendar for selected month
            function updateCalendarForMonth(month, year) {
                // Update the global state month/year if they exist
                if (typeof state !== 'undefined') {
                    state.currentMonth = month;
                    state.currentYear = year;
                }
                
                // Update current date to selected month
                currentDate = new Date(year, month, 1);
                
                // Re-render calendar if function exists
                if (typeof renderCalendar === 'function') {
                    renderCalendar();
                } else if (typeof generateCalendar === 'function') {
                    generateCalendar();
                } else {
                    // Fallback: manually update calendar
                    updateCalendarDisplay(month, year);
                }
            }
            
            // Fallback function to update calendar display
            function updateCalendarDisplay(month, year) {
                const calendarBody = document.getElementById('calendarBody');
                if (!calendarBody) return;
                
                const firstDay = new Date(year, month, 1);
                const lastDay = new Date(year, month + 1, 0);
                const daysInMonth = lastDay.getDate();
                const startingDayOfWeek = firstDay.getDay();
                
                // Clear existing calendar
                calendarBody.innerHTML = '';
                
                // Check if responsive mode
                const isResponsive = window.innerWidth <= 768;
                
                if (isResponsive) {
                    // Generate responsive calendar with day-column structure
                    generateResponsiveCalendar(calendarBody, month, year, firstDay, daysInMonth, startingDayOfWeek);
                } else {
                    // Generate desktop calendar with calendar-day structure
                    generateDesktopCalendar(calendarBody, month, year, firstDay, daysInMonth, startingDayOfWeek);
                }
            }
            
            // Generate responsive calendar structure (weekdays only: Mon-Fri, flexible rows)
            function generateResponsiveCalendar(calendarBody, month, year, firstDay, daysInMonth, startingDayOfWeek) {
                // Generate calendar for weekdays only (Monday to Friday)
                for (let day = 1; day <= daysInMonth; day++) {
                    const dayDateObj = new Date(year, month, day);
                    const dayOfWeek = dayDateObj.getDay(); // 0=Sunday, 1=Monday, ..., 6=Saturday
                    
                    // Only include weekdays (Monday=1 to Friday=5)
                    if (dayOfWeek >= 1 && dayOfWeek <= 5) {
                        const dayColumn = document.createElement('div');
                        dayColumn.className = 'day-column';
                        
                        const dayDate = document.createElement('div');
                        dayDate.className = 'day-date';
                        dayDate.textContent = day;
                        
                        // Check if today
                        const today = new Date();
                        if (dayDateObj.toDateString() === today.toDateString()) {
                            dayDate.classList.add('today');
                        }
                        
                        const appointmentsContainer = document.createElement('div');
                        appointmentsContainer.className = 'appointments-container';
                        
                        // Add appointments if any
                        const dayAppointments = getAppointmentsForDate(dayDateObj);
                        dayAppointments.forEach(appointment => {
                            const appointmentElement = document.createElement('div');
                            appointmentElement.className = 'appointment';
                            appointmentElement.innerHTML = `
                                <div class="appointment-time">${appointment.time}</div>
                                <div class="appointment-patient">${appointment.patient}</div>
                                <div class="appointment-professional">${appointment.professional}</div>
                            `;
                            
                            // Add click event for appointments in responsive mode
                            appointmentElement.addEventListener('click', function(e) {
                                e.stopPropagation(); // Prevent day selection
                                if (window.innerWidth <= 768) {
                                    // Store appointment data for responsive modal
                                    state.selectedAppointment = appointment;
                                    openResponsiveAppointmentModal(appointment);
                                }
                            });
                            
                            appointmentsContainer.appendChild(appointmentElement);
                        });
                        
                        // Add "Agregar Turno" button for responsive mode
                        const addAppointmentBtn = document.createElement('button');
                        addAppointmentBtn.className = 'add-appointment-btn';
                        addAppointmentBtn.innerHTML = `
                            <span class="add-btn-line1">+</span>
                            <span class="add-btn-line2">Agregar Turno</span>
                        `;
                        
                        // Add click event for add appointment button
                        addAppointmentBtn.addEventListener('click', function(e) {
                            e.stopPropagation(); // Prevent day selection
                            if (window.innerWidth <= 768) {
                                // Set the selected date for new appointment
                                const selectedDate = `${year}-${String(month + 1).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
                                document.getElementById('appointmentDate').value = selectedDate;
                                
                                // Reset modal footer to original state
                                resetAppointmentModalFooter();
                                
                                // Open appointment modal for new appointment
                                openModal('appointmentModal');
                            }
                        });
                        
                        appointmentsContainer.appendChild(addAppointmentBtn);
                        
                        // Add click event for day selection
                        dayColumn.addEventListener('click', function() {
                            document.querySelectorAll('.day-column').forEach(d => d.classList.remove('selected'));
                            this.classList.add('selected');
                            
                            // Update selected date
                            if (typeof state !== 'undefined') {
                                state.selectedDate = new Date(year, month, day);
                            }
                        });
                        
                        dayColumn.appendChild(dayDate);
                        dayColumn.appendChild(appointmentsContainer);
                        calendarBody.appendChild(dayColumn);
                    }
                }
            }
            
            // Generate desktop calendar structure
            function generateDesktopCalendar(calendarBody, month, year, firstDay, daysInMonth, startingDayOfWeek) {
                // Adjust startingDayOfWeek for Monday-first calendar (0=Monday, 6=Sunday)
                let adjustedStartingDay = startingDayOfWeek === 0 ? 6 : startingDayOfWeek - 1;
                
                // Add empty cells for days before the first day of the month
                for (let i = 0; i < adjustedStartingDay; i++) {
                    const emptyDay = document.createElement('div');
                    emptyDay.className = 'calendar-day empty';
                    calendarBody.appendChild(emptyDay);
                }
                
                // Add days of the month
                for (let day = 1; day <= daysInMonth; day++) {
                    const dayElement = document.createElement('div');
                    dayElement.className = 'calendar-day';
                    dayElement.textContent = day;
                    
                    // Check if this day has appointments
                    const dayDate = new Date(year, month, day);
                    const hasAppointments = checkAppointmentsForDate(dayDate);
                    
                    if (hasAppointments) {
                        dayElement.classList.add('has-appointments');
                    }
                    
                    // Add click event for day selection
                    dayElement.addEventListener('click', function() {
                        document.querySelectorAll('.calendar-day').forEach(d => d.classList.remove('selected'));
                        this.classList.add('selected');
                        
                        // Update selected date
                        if (typeof state !== 'undefined') {
                            state.selectedDate = new Date(year, month, day);
                        }
                    });
                    
                    calendarBody.appendChild(dayElement);
                }
            }
            
            // Helper function to get appointments for a specific date
            function getAppointmentsForDate(date) {
                if (typeof state !== 'undefined' && state.appointments) {
                    return state.appointments.filter(appointment => {
                        const appointmentDate = new Date(appointment.date);
                        return appointmentDate.toDateString() === date.toDateString();
                    });
                }
                return [];
            }
            
            // Helper function to check appointments for a specific date
            function checkAppointmentsForDate(date) {
                if (typeof state !== 'undefined' && state.appointments) {
                    return state.appointments.some(appointment => {
                        const appointmentDate = new Date(appointment.date);
                        return appointmentDate.toDateString() === date.toDateString();
                    });
                }
                return false;
            }
            
            // Function to update sections for selected month
            function updateSectionsForMonth(month, year) {
                // Filter appointments for selected month if appointments exist
                if (typeof state !== 'undefined' && state.appointments) {
                    const selectedDate = new Date(year, month);
                    const monthStart = new Date(year, month, 1);
                    const monthEnd = new Date(year, month + 1, 0);
                    
                    // Update professionals data
                    if (typeof displayProfessionalList === 'function') {
                        displayProfessionalList();
                    }
                    
                    // Update KPIs for selected month
                    if (typeof renderProfessionalKPIs === 'function') {
                        renderProfessionalKPIs();
                    }
                    
                    // Update box availability
                    if (typeof updateStats === 'function') {
                        updateStats();
                    }
                    
                    // Sync mobile data after updates
                    setTimeout(() => {
                        syncMobileData('professionals');
                        syncMobileData('boxes');
                        syncMobileData('kpis');
                    }, 500);
                }
            }
        });
        
        // Also ensure it displays when the window is fully loaded
        window.addEventListener('load', function() {
            setTimeout(() => displayProfessionalList(), 100);
        });
        
        // Login functionality
        document.addEventListener('DOMContentLoaded', function() {
            const loginOverlay = document.getElementById('loginOverlay');
            const loginForm = document.getElementById('loginForm');
            const mainContent = document.getElementById('mainContent');
            const usernameInput = document.getElementById('username');
            const passwordInput = document.getElementById('password');
            
            // Check session on page load
            checkSession();
            
            // Handle login form submission
            loginForm.addEventListener('submit', function(e) {
                e.preventDefault();
                
                const username = usernameInput.value.trim();
                const password = passwordInput.value.trim();
                
                if (username && password) {
                    // Send login request to PHP
                    fetch('auth/login.php', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            username: username,
                            password: password
                        })
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            // Hide login modal with animation
                            loginOverlay.style.opacity = '0';
                            loginOverlay.style.visibility = 'hidden';
                            
                            // Show main content
                            mainContent.classList.remove('main-content-hidden');
                            
                            // Remove login overlay from DOM after animation
                            setTimeout(() => {
                                loginOverlay.style.display = 'none';
                            }, 300);
                            
                            // Update user info in header
                            updateUserInfo(data.user);
                            
                        } else {
                            alert(data.message || 'Error de autenticación');
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        alert('Error de conexión. Intenta nuevamente.');
                    });
                } else {
                    alert('Por favor, ingresa usuario y contraseña');
                }
            });
            
            // Function to check session
            function checkSession() {
                fetch('auth/check_session.php')
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.logged_in) {
                        // User is already logged in
                        loginOverlay.style.display = 'none';
                        mainContent.classList.remove('main-content-hidden');
                        updateUserInfo(data.user);
                    } else {
                        // Show login modal
                        loginOverlay.style.display = 'flex';
                        mainContent.classList.add('main-content-hidden');
                    }
                })
                .catch(error => {
                    console.error('Error checking session:', error);
                    // Show login modal on error
                    loginOverlay.style.display = 'flex';
                    mainContent.classList.add('main-content-hidden');
                });
            }
            
            // Function to update user info in UI
            function updateUserInfo(user) {
                const userNameSpan = document.getElementById('userNameDisplay');
                const dropdownUserName = document.getElementById('dropdownUserName');
                const userAvatar = document.querySelector('.user-avatar');
                
                const displayName = user.nombre_completo || user.username;
                
                if (userNameSpan) {
                    userNameSpan.textContent = displayName;
                }
                if (dropdownUserName) {
                    dropdownUserName.textContent = displayName;
                }
                if (userAvatar) {
                    userAvatar.textContent = user.username.charAt(0).toUpperCase();
                }
            }
            
            // User dropdown functionality
            const userInfo = document.getElementById('userInfo');
            const userDropdown = document.getElementById('userDropdown');
            const logoutBtn = document.getElementById('logoutBtn');
            
            // Toggle dropdown on user info click
            userInfo.addEventListener('click', function(e) {
                e.stopPropagation();
                userDropdown.classList.toggle('show');
            });
            
            // Close dropdown when clicking outside
            document.addEventListener('click', function(e) {
                if (!userInfo.contains(e.target) && !userDropdown.contains(e.target)) {
                    userDropdown.classList.remove('show');
                }
            });
            
            // Logout functionality
            logoutBtn.addEventListener('click', function() {
                // Send logout request to PHP
                fetch('auth/logout.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Hide dropdown
                        userDropdown.classList.remove('show');
                        
                        // Show login modal
                        loginOverlay.style.display = 'flex';
                        loginOverlay.style.opacity = '1';
                        loginOverlay.style.visibility = 'visible';
                        
                        // Hide main content
                        mainContent.classList.add('main-content-hidden');
                        
                        // Clear form fields
                        usernameInput.value = '';
                        passwordInput.value = '';
                        
                        // Focus on username field
                        setTimeout(() => {
                            usernameInput.focus();
                        }, 300);
                    }
                })
                .catch(error => {
                    console.error('Error during logout:', error);
                    // Force logout on client side even if server request fails
                    userDropdown.classList.remove('show');
                    loginOverlay.style.display = 'flex';
                    loginOverlay.style.opacity = '1';
                    loginOverlay.style.visibility = 'visible';
                    mainContent.classList.add('main-content-hidden');
                    usernameInput.value = '';
                    passwordInput.value = '';
                });
            });
        });
    </script>
    <!-- Scripts -->
    <script>
        // Función para cerrar cualquier modal
        function closeModal(modalId) {
            const modal = document.getElementById(modalId);
            if (modal) {
                modal.style.display = 'none';
                document.body.style.overflow = 'auto';
            }
        }
        
        // Cerrar modal al hacer clic fuera del contenido
        window.onclick = function(event) {
            const modals = document.querySelectorAll('.modal');
            modals.forEach(modal => {
                if (event.target === modal) {
                    closeModal(modal.id);
                }
            });
        };
    </script>
</body>
</html>
