<?php
session_start();
require_once '../config/database.php';

header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Método no permitido']);
    exit;
}

$input = json_decode(file_get_contents('php://input'), true);

if (!isset($input['username']) || !isset($input['password'])) {
    echo json_encode(['success' => false, 'message' => 'Usuario y contraseña requeridos']);
    exit;
}

$username = trim($input['username']);
$password = trim($input['password']);

if (empty($username) || empty($password)) {
    echo json_encode(['success' => false, 'message' => 'Usuario y contraseña no pueden estar vacíos']);
    exit;
}

try {
    $db = new Database();
    
    // Buscar usuario en la base de datos
    $usuarios = $db->select('usuarios', [
        'username' => $username,
        'password' => $password,
        'activo' => 1
    ]);
    
    if (count($usuarios) === 1) {
        $usuario = $usuarios[0];
        
        // Actualizar último acceso
        $db->query("UPDATE usuarios SET ultimo_acceso = NOW() WHERE id = :id", ['id' => $usuario['id']]);
        
        // Crear sesión
        $_SESSION['user_id'] = $usuario['id'];
        $_SESSION['username'] = $usuario['username'];
        $_SESSION['nombre_completo'] = $usuario['nombre_completo'];
        $_SESSION['rol'] = $usuario['rol'];
        $_SESSION['login_time'] = time();
        
        echo json_encode([
            'success' => true,
            'message' => 'Login exitoso',
            'user' => [
                'username' => $usuario['username'],
                'nombre_completo' => $usuario['nombre_completo'],
                'rol' => $usuario['rol']
            ]
        ]);
    } else {
        echo json_encode(['success' => false, 'message' => 'Credenciales incorrectas']);
    }
    
} catch (Exception $e) {
    error_log("Error en login: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'Error interno del servidor']);
}
?>
