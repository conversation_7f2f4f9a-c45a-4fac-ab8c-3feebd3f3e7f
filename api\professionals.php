<?php
session_start();

header('Content-Type: application/json; charset=utf-8');

// Verificación de sesión
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'error' => 'No autorizado']);
    exit;
}

require_once __DIR__ . '/../config/database.php';

$database = new Database();
$pdo = $database->getConnection();

$method = $_SERVER['REQUEST_METHOD'];
$action = $_GET['action'] ?? $_POST['action'] ?? null;

function json_error($message, $code = 400) {
    http_response_code($code);
    echo json_encode(['success' => false, 'error' => $message]);
    exit;
}

function sanitize_str($value) {
    return trim(filter_var($value, FILTER_SANITIZE_STRING));
}

try {
    if ($method === 'GET') {
        // Listar profesionales
        // Filtros opcionales: status=Vigente|Vacaciones|Licencia|Permiso|Cursos
        $status = isset($_GET['status']) ? sanitize_str($_GET['status']) : null;

        $sql = "SELECT id, nombre, estado, estado_desde, estado_hasta, created_at, updated_at FROM profesionales";
        $params = [];
        if ($status) {
            $sql .= " WHERE estado = :estado";
            $params[':estado'] = $status;
        }
        $sql .= " ORDER BY nombre ASC";

        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);
        $rows = $stmt->fetchAll(PDO::FETCH_ASSOC);

        echo json_encode(['success' => true, 'data' => $rows]);
        exit;
    }

    if ($method === 'POST') {
        if (!$action) {
            json_error('Acción no especificada');
        }

        if ($action === 'create') {
            $nombre = isset($_POST['nombre']) ? sanitize_str($_POST['nombre']) : '';
            if ($nombre === '') {
                json_error('El nombre es requerido');
            }

            // Validar duplicado por nombre (case-insensitive)
            $dupStmt = $pdo->prepare('SELECT id FROM profesionales WHERE LOWER(nombre) = LOWER(:nombre) LIMIT 1');
            $dupStmt->execute([':nombre' => $nombre]);
            if ($dupStmt->fetch()) {
                json_error('Ya existe un profesional con ese nombre');
            }

            $stmt = $pdo->prepare('INSERT INTO profesionales (nombre, estado, created_at, updated_at) VALUES (:nombre, :estado, NOW(), NOW())');
            $stmt->execute([
                ':nombre' => $nombre,
                ':estado' => 'Vigente',
            ]);

            echo json_encode(['success' => true, 'id' => $pdo->lastInsertId()]);
            exit;
        }

        if ($action === 'update_status') {
            $id = isset($_POST['id']) ? (int)$_POST['id'] : 0;
            $estado = isset($_POST['estado']) ? sanitize_str($_POST['estado']) : '';
            $estado_desde = isset($_POST['estado_desde']) && $_POST['estado_desde'] !== '' ? $_POST['estado_desde'] : null;
            $estado_hasta = isset($_POST['estado_hasta']) && $_POST['estado_hasta'] !== '' ? $_POST['estado_hasta'] : null;

            if ($id <= 0 || $estado === '') {
                json_error('Parámetros inválidos');
            }

            $valid_states = ['Vigente', 'Vacaciones', 'Licencia', 'Permiso', 'Cursos'];
            if (!in_array($estado, $valid_states, true)) {
                json_error('Estado inválido');
            }

            $stmt = $pdo->prepare('UPDATE profesionales SET estado = :estado, estado_desde = :desde, estado_hasta = :hasta, updated_at = NOW() WHERE id = :id');
            $stmt->execute([
                ':estado' => $estado,
                ':desde' => $estado_desde,
                ':hasta' => $estado_hasta,
                ':id' => $id,
            ]);

            echo json_encode(['success' => true]);
            exit;
        }

        if ($action === 'delete') {
            $id = isset($_POST['id']) ? (int)$_POST['id'] : 0;
            if ($id <= 0) {
                json_error('ID inválido');
            }

            $stmt = $pdo->prepare('DELETE FROM profesionales WHERE id = :id');
            $stmt->execute([':id' => $id]);

            echo json_encode(['success' => true]);
            exit;
        }

        json_error('Acción no soportada', 405);
    }

    // Métodos no soportados
    json_error('Método no permitido', 405);
} catch (Throwable $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'error' => 'Error interno', 'detail' => $e->getMessage()]);
}
